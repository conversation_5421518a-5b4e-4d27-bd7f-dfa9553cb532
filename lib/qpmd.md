package com.au.storetrack.Fragments.Task;


/*
 * mo, 17/5/18, qp_index is not used any more. So should be deleted in every functions.
 * */


import android.app.AlertDialog;
import android.app.DatePickerDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputType;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextWatcher;
import android.text.style.BackgroundColorSpan;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.SearchView;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.core.content.ContextCompat;
import androidx.core.graphics.drawable.RoundedBitmapDrawable;
import androidx.core.graphics.drawable.RoundedBitmapDrawableFactory;

import com.au.storetrack.Activities.ActivityBase;
import com.au.storetrack.Adapters.SpinnerAdapter;
import com.au.storetrack.Fragments.Base.FragmentBase;
import com.au.storetrack.Fragments.Task.MPT.CameraTuple;
import com.au.storetrack.Fragments.Task.MPT.GalleryInfo;
import com.au.storetrack.Fragments.Task.MPT.GalleryPhoto;
import com.au.storetrack.Fragments.Task.MPT.GallerySection;
import com.au.storetrack.Fragments.Task.MPT.MPTListFragment;
import com.au.storetrack.Model.DataModel.FormModel;
import com.au.storetrack.Model.DataModel.PhotoTagModel;
import com.au.storetrack.Model.DataModel.QuestionAnswerModel;
import com.au.storetrack.Model.DataModel.QuestionConditionModel;
import com.au.storetrack.Model.DataModel.QuestionMeasurementConditionModel;
import com.au.storetrack.Model.DataModel.QuestionMeasurementModel;
import com.au.storetrack.Model.DataModel.QuestionMeasurementOptionModel;
import com.au.storetrack.Model.DataModel.QuestionMeasurementValidationModel;
import com.au.storetrack.Model.DataModel.QuestionModel;
import com.au.storetrack.Model.DataModel.QuestionPartModel;
import com.au.storetrack.Model.DataModel.TaskDetailModel;
import com.au.storetrack.Model.EventModel.APIEvent;
import com.au.storetrack.R;
import com.au.storetrack.Utilities.CommonFunction;
import com.au.storetrack.Utilities.Constant;
import com.au.storetrack.Utilities.ControlParser;
import com.au.storetrack.Utilities.Database.DatabaseManager;
import com.au.storetrack.Utilities.Network.API;
import com.au.storetrack.Utilities.Storage.FileProvider;
import com.au.storetrack.Utilities.ValidationUtils;
import com.au.storetrack.Utilities.misc.ImageFragment;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import io.realm.RealmList;

/**
 * Created by Augustin on 09/3/2021.
 */
public class QuestionPartMeasurementDetailFragment extends FragmentBase {

    public static final String TASK_FORM_NAVIGATION_KEY = "TASK_FORM_NAVIGATION_KEY";
    public static final String QUESTION_PART_MEASUREMENT_DETAIL_TITLE_KEY = "QUESTION_PART_MEASUREMENT_DETAIL_TITLE_KEY";
    public static final String QUESTION_PART_DESC_TITLE_KEY = "QUESTION_PART_DESC_TITLE_KEY";
    public static final String FORM_QUESTION_PART_DETAIL_TITLE_KEY = "FORM_QUESTION_PART_DETAIL_TITLE_KEY";

    private static final String FORM_LAYOUT_UI_CONTAINER_OBJECT_KEY = "UI_CONTAINER_OBJECT";
    private static final String FORM_LAYOUT_UI_INPUT_OBJECT_KEY = "UI_INPUT_OBJECT";
    private static final String FORM_LAYOUT_UI_ERROR_MESSAGE_OBJECT_KEY = "UI_ERROR_MESSAGE_OBJECT";

    private DateFormat leaveDisplayDateFormat = new SimpleDateFormat(Constant.LONG_DATEFORMAT_STRING, Constant.DEFAULT_LOCALE_INSTANCE);
    private LinearLayout taskFormQuestionContainer_linearLayout;
    private String taskID;
    private String formID;
    private String questionMultiPartID;
    private QuestionModel questionModel;
    private FormModel formModel;
    private TaskDetailModel taskModel;    //mo, to check if a task is mpt task

    //mo, this hashmap contains all question ids >> qp ids >> m ids
    private final HashMap< String, Object > formLayout_hashMap = new HashMap<>();

    private TextView selectedQuestionMeasurementMultiSelect_textView = null;
    private final ArrayList< String > selectedQuestionMeasurementMultiItems = new ArrayList<>();

    ArrayList< String > skippedQ = new ArrayList<>();
    ArrayList< String > allQuestionsIDs = new ArrayList<>();
    private final ArrayList< String > skippedQuestions = new ArrayList<>();
    private final ArrayList< String > notSkippedQuestions = new ArrayList<>();
    private RealmList< QuestionAnswerModel > questionAnswerModels = new RealmList<>();

    private boolean isMidFormSaving = false;
    private boolean isAnswerValidated = true;
    private boolean isPhotoValidated = true;
    private String selectedLocalQuestionPhotoPath = null;

    private TextView  selectedStartOrEndDateValue_textView;
    SearchView searchView;
    List<TextView> highlightedViews = new ArrayList<>();
    ScrollView scrollView;
    TextView searchItemCount;
    TextView totalCheck;
    TextView totalCheckTV;

    int totalCheckInt = 0;
    private Button bigSaveButton;




   private boolean resetSpinner;


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        EventBus.getDefault().register(this);

       // Log.d("i m", "here");

        //inflate task form detail fragment
        View v = inflater.inflate(R.layout.task_form_detail_fragment_v2, container, false);

        resetSpinner = false;

        Bundle receiveBundle = getArguments();

        searchView = ControlParser.parseControl(v, R.id.searchView);
        searchItemCount = ControlParser.parseControl(v,R.id.searchItem_count);
        totalCheck = ControlParser.parseControl(v,R.id.totalCheckedValue);
        totalCheckTV = ControlParser.parseControl(v,R.id.totalChecked_tv);

        
        if (receiveBundle != null) {
            if (receiveBundle.containsKey(TaskDetailModel.TASK_ID_KEY) &&
                    (receiveBundle.containsKey(FormModel.FORM_ID_KEY)) &&
                    (receiveBundle.containsKey(QuestionModel.QUESTION_ID_KEY)) &&
                    (receiveBundle.containsKey(QuestionPartModel.QUESTION_PART_ID_KEY)) &&
                    (receiveBundle.containsKey(QuestionPartModel.QUESTION_PART_MULTI_ID_KEY))) {

                taskID = receiveBundle.getString(TaskDetailModel.TASK_ID_KEY);
                formID = receiveBundle.getString(FormModel.FORM_ID_KEY);
                String questionID = receiveBundle.getString(QuestionModel.QUESTION_ID_KEY);
                questionMultiPartID = receiveBundle.getString(QuestionPartModel.QUESTION_PART_MULTI_ID_KEY);

                taskModel = DatabaseManager.getInstance(getContext()).getTaskDetailModelWithTaskID(ActivityBase.mRealm, taskID, false);
                formModel = DatabaseManager.getInstance(getContext()).getFormModelFromTaskIDWithFormID(ActivityBase.mRealm, taskID, formID, true);
                questionModel = DatabaseManager.getInstance(getContext()).getFormQuestionDetailModelWithQuestionID(ActivityBase.mRealm, formModel, questionID, true);
            }
        }
        Log.e("qpmpid", questionMultiPartID);
        //error if task detail request == null
        if ((taskID == null) || (formID == null) || (formModel == null)) {
            AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
            builder.setTitle("Fail to initialise");
            builder.setMessage("Please try again later.");
            builder.setCancelable(false);
            builder.setPositiveButton(R.string.OK, new DialogInterface.OnClickListener() {
                public void onClick(DialogInterface dialog, int id) {
                    popFragment();
                }
            });

            builder.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    CommonFunction.getInstance(getContext()).dismissAlertDialog((AlertDialog) dialog);
                }
            });

            AlertDialog alertDialog = builder.create();
            alertDialog.show();

            CommonFunction.getInstance(getContext()).recordShownAlertDialog(alertDialog);
        }
        //get form questions
        else {
          //  CommonFunction.print("formModel: QM" + formModel + " : " + questionModel.getQuestionID() + " : " + questionModel);

            taskFormQuestionContainer_linearLayout = ControlParser.parseControl(v, R.id.taskFormQuestionContainer_linearLayout);
            bigSaveButton = ControlParser.parseControl(v, R.id.green_save_button);
            bigSaveButton.setVisibility(View.VISIBLE);



            bigSaveButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    bigSaveButton.setEnabled(false);

                    saveButtonClicked();

                    bigSaveButton.setEnabled(true);


                }
            });

            //show question title
            String questionTitle = requireArguments().getString(QUESTION_PART_DESC_TITLE_KEY);
            TextView textView = v.findViewById(R.id.questionMeasurementTitle);
            textView.setVisibility(View.VISIBLE);
            textView.setText(questionTitle);
            loadQuestionPartsMeasurements();
            //mo, populate Question
        }

        return v;
    }

    public List<TextView> searchInLinearLayout(LinearLayout linearLayout, String query) {
        List<TextView> matches = new ArrayList<>();
        for (int i = 0; i < linearLayout.getChildCount(); i++) {
            View view = linearLayout.getChildAt(i);
            if (view instanceof LinearLayout) {
                List<TextView> childMatches = searchInLinearLayout((LinearLayout) view, query);
                matches.addAll(childMatches);
            } else if (view instanceof TextView) {
                String text = ((TextView) view).getText().toString().toLowerCase(Locale.getDefault());
                if (text.contains(query.toLowerCase(Locale.getDefault()))) {
                    matches.add((TextView) view);
                }
            }
        }
        return matches;
    }

    //mo, 13/11/17, Below is called even after 'Save' button before popping fragment. So i needed new variable isExitAfterSaveSuccess.
    @Override
    public void onDestroyView() {
        try {
          saveFunction(false);

            CommonFunction.hideSoftKeyboard(requireActivity());
            EventBus.getDefault().unregister(this);
        } catch (Exception e) {
            CommonFunction.print("error!");
        }
        super.onDestroyView();
    }

    @Subscribe
    public void onAPIEventReceived(APIEvent event) {
        if (API.API_DOWNLOAD_FILE_REQUEST_TAG.equals(event.getApiRequestTag())) {
            if (event.getApiResponseType().equalsIgnoreCase(API.API_SUCCESS_RESPONSE_TYPE)) {
                if ((selectedLocalQuestionPhotoPath != null) && (!CommonFunction.isEmptyStringField(selectedLocalQuestionPhotoPath))) {
                    CommonFunction.openFile(getContext(), selectedLocalQuestionPhotoPath);
                }
                CommonFunction.getInstance(getContext()).hideProgressIndicator();
            } else if (event.getApiResponseType().equalsIgnoreCase(API.API_FAILED_RESPONSE_TYPE)) {
                CommonFunction.getInstance(getContext()).hideProgressIndicator();
            }

            selectedLocalQuestionPhotoPath = null;
        }
    }

    @Override
    public void setNavigationBarContent() {

        navigationBar.clearAllControls();

        //set form title
        String fragmentTitle = requireArguments().getString(QUESTION_PART_MEASUREMENT_DETAIL_TITLE_KEY);
        navigationBar.setNavigationBarTitleTextMenuPage((fragmentTitle != null ? fragmentTitle : "Question Part Measurement"));

        ImageView rightControl = new ImageView(rootActivity);
        rightControl.setImageResource(R.drawable.icon_save);

        //save clicked
        rightControl.setOnClickListener(new View.OnClickListener() {
            @Override

            public void onClick(View v) {
                    saveButtonClicked();
            }
        });

        ImageView leftControl = new ImageView(rootActivity);
        leftControl.setImageResource(R.drawable.btn_back);
        leftControl.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                // If it is not vision form, open the form detail screen
                saveFunction(false);
                backButtonPressed(true,false);

            }
        });

        navigationBar.addLeftNavigationBarControl(leftControl);
        navigationBar.addRightNavigationBarControl(rightControl);

    }


    public void saveButtonClicked() {
        //----------------------------------------
        //mo, Save button clicked (Save QuestionPartM)
        //----------------------------------------

        bigSaveButton.setText("Saving...");

        measurementValidation();


        // mo, when not saving, and just pressed back button, does it still save answers? where to save?
        // YES, ALL SAVED INTO Form >> formQuestionAnswers
        // If then, why questionAnswerModel is null on populateQuestion() when testing? a. I don't know. Maybe deleted when sync or loging out.
        if ((formModel.getFormQuestions() != null) && (formModel.getFormQuestions().size() > 0)) {
            //mo, validation happens in below function.

            String errorMessage = "";
            if (!isAnswerValidated && !isPhotoValidated) {
                errorMessage = "Please check your inputs and mandatory (yellow) photos.";
            } else {
                if (!isAnswerValidated) {
                    errorMessage = "Please check your inputs.";
                } else if (!isPhotoValidated) {
                    errorMessage = errorMessage + "Please check your mandatory (yellow) photos.";
                }
            }

            if (!isAnswerValidated || !isPhotoValidated) {
                //Validations failed
                CommonFunction.enableUserInteraction(requireActivity());
                saveFunction(false);

                AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
                builder.setTitle("Error!");
                builder.setMessage(errorMessage);
                builder.setPositiveButton(R.string.OK, new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int id) {

                    }
                });

                builder.setOnDismissListener(new DialogInterface.OnDismissListener() {
                    @Override
                    public void onDismiss(DialogInterface dialog) {
                        CommonFunction.getInstance(getContext()).dismissAlertDialog((AlertDialog) dialog);
                    }
                });

                AlertDialog alertDialog = builder.create();
                alertDialog.show();

                CommonFunction.getInstance(getContext()).recordShownAlertDialog(alertDialog);
            } else {
                //all good

                long start = System.currentTimeMillis();
                saveFunction(true);
                long end = System.currentTimeMillis();
                System.out.println("Time Elapsed savefun : "+(end-start));

                try{
                    backButtonPressed(false,true);;

                }catch(Exception e){

                }

            }
        }

        bigSaveButton.setText("Saved");
    }

    public void measurementValidation() {

        isMidFormSaving = false;
        isAnswerValidated = true;
        isPhotoValidated = true;


        questionAnswerModels = generateAllQuestionAnswerModel();

        QuestionPartMeasurementDetailFragment.QuestionSaveInfo objInfo = new QuestionSaveInfo();
        ArrayList< String > listMandatoryQuestionsID = new ArrayList<>();
        ArrayList< String > AnswerQ = new ArrayList<>();

        int count = 0;

        boolean isQuestionMandatory = DatabaseManager.getInstance(getContext()).isQuestionMandatory(taskID,formModel, questionModel);

        if (isQuestionMandatory) {
            objInfo.qid = questionModel.getQuestionID();
            objInfo.has_m_mandatory = true;

            listMandatoryQuestionsID.add(questionModel.getQuestionID());
        }

        for (QuestionAnswerModel questionAns : questionAnswerModels) {

            if (listMandatoryQuestionsID.contains(questionAns.getQuestionAnswerQuestionID())) {
                objInfo.is_q_answered = true;
                AnswerQ.add(questionAns.getQuestionAnswerQuestionID());
            }
        }

        for (String s : listMandatoryQuestionsID) {

            if (AnswerQ.contains(s)) {
                count++;
            }
        }



        if (listMandatoryQuestionsID.size() == 0) {
            objInfo.has_m_mandatory = false;
        }

        if ((objInfo.has_m_mandatory && !objInfo.is_q_answered) || (objInfo.has_m_mandatory && !(count == listMandatoryQuestionsID.size()))) {
            isAnswerValidated = false;
        }

    }

    public void saveFunction(Boolean validated) {

        if(!validated){
            measurementValidation();

        }

        if ((formModel.getFormQuestions() != null) && (questionAnswerModels.size() > 0) && (questionModel != null)) {
            DatabaseManager.getInstance(getContext()).saveFormWithSkippedForTaskIDWithFormID(ActivityBase.mRealm, taskID, formModel, questionModel, questionAnswerModels, skippedQuestions, notSkippedQuestions, (isAnswerValidated && isPhotoValidated));
        }

        requireView().requestFocus();
        CommonFunction.hideSoftKeyboard(requireActivity());

    }

    public void backButtonPressed(Boolean backButtonPressed, Boolean saveButtonClicked) {
        Bundle fragmentBundle = new Bundle();

        String fragmentNavigator = requireArguments().getString(TASK_FORM_NAVIGATION_KEY);
        fragmentBundle.putString(FormQuestionPartDetailFragment.TASK_FORM_NAVIGATION_KEY, fragmentNavigator);
        fragmentBundle.putString(TaskDetailModel.TASK_ID_KEY, taskID);
        fragmentBundle.putString(FormModel.FORM_ID_KEY, formModel.getFormID());
        //Nirvik, this sends back the title of the form
        fragmentBundle.putString(FORM_QUESTION_PART_DETAIL_TITLE_KEY, getArguments().getString(QUESTION_PART_MEASUREMENT_DETAIL_TITLE_KEY));

//        String scrollFQY = getArguments().getString("previousPositionFQ");
//        String scrollY = getArguments().getString("previousPosition");
//        fragmentBundle.putString("previousPosition", scrollY);
//        fragmentBundle.putString("previousPositionFQ",scrollFQY);


       // boolean isQuestionAnswered = DatabaseManager.getInstance(getContext()).isQuestionAnsweredSubmitForm(taskID, formModel, questionModel);

        if (backButtonPressed || saveButtonClicked || questionModel.hasSignature()) {
            if(questionModel.getQuestionParts().size() > 1){
                if(questionModel.isQuestionMulti()) {
                    fragmentBundle.putString(QuestionModel.QUESTION_ID_KEY, questionModel.getQuestionID());
                    replaceFragment(new FormQuestionPartDetailFragment(), fragmentBundle, FragmentBase.SLIDE_IN_FROM_LEFT_ANIMATION_KEY);
                }else{
                    fragmentBundle.putString(QuestionModel.QUESTION_ID_KEY, questionModel.getQuestionID());
                    replaceFragment(new SubheaderFragment(), fragmentBundle, FragmentBase.SLIDE_IN_FROM_LEFT_ANIMATION_KEY);
                }
            }else{
                fragmentBundle.putString(TaskFormDetailFragment.TASK_FORM_DETAIL_TITLE_KEY, formModel.getFormName());
                replaceFragment(new TaskFormDetailFragment(), fragmentBundle, FragmentBase.SLIDE_IN_FROM_LEFT_ANIMATION_KEY);
            }

        } else {
            fragmentBundle.putString(TaskFormDetailFragment.TASK_FORM_DETAIL_TITLE_KEY, formModel.getFormName());
            replaceFragment(new TaskFormDetailFragment(), fragmentBundle, FragmentBase.SLIDE_IN_FROM_LEFT_ANIMATION_KEY);
        }

//        if (!isQuestionAnswered || (backButtonPressed || questionModel.isQuestionMulti()) || questionModel.hasSignature()) {
//            fragmentBundle.putString(QuestionModel.QUESTION_ID_KEY, questionModel.getQuestionID());
//            replaceFragment(new FormQuestionPartDetailFragment(), fragmentBundle, FragmentBase.SLIDE_IN_FROM_LEFT_ANIMATION_KEY);
//
//        } else {
//            fragmentBundle.putString(TaskFormDetailFragment.TASK_FORM_DETAIL_TITLE_KEY, formModel.getFormName());
//            replaceFragment(new TaskFormDetailFragment(), fragmentBundle, FragmentBase.SLIDE_IN_FROM_LEFT_ANIMATION_KEY);
//        }
//    }


    }

    //mo, called by Back button pressed.
    //Called by 3 important places
    //(1) onDestroy() by Android back button (normal back button also fires this) >> and save intermediate changes
    //(2) Save button pressed
    //(3) Back button pressed >> xxx will be saved incomplete state >> OK pressed (isn't it duplicate with (1)?)
    //(4) Convert realmList to arraylist
    //mo, 30/5/19, Validation seems to happen here as well : isAnswerValidated

    private RealmList< QuestionAnswerModel > generateAllQuestionAnswerModel() {
        RealmList< QuestionAnswerModel > questionAnswerModels = new RealmList<>();

        if (!CommonFunction.isEmptyStringField(questionModel.getQuestionDescription())) {

            HashMap< String, Object > questionMap = (HashMap< String, Object >) formLayout_hashMap.get(questionModel.getQuestionID());

            if (Objects.requireNonNull(questionMap).containsKey(FORM_LAYOUT_UI_CONTAINER_OBJECT_KEY)) {
                ViewGroup questionContainer = (ViewGroup) questionMap.get(FORM_LAYOUT_UI_CONTAINER_OBJECT_KEY);

                //----------------
                // 1. Question Answer Build
                //----------------
                if (!questionModel.isQuestionIsComment()) {
                    //----------------
                    // STANDARD QUESTIONS
                    //----------------
                    if (formModel.getFormQuestions().size() > 0) {

//                        for (QuestionModel questionModels : formModel.getFormQuestions()) {

                            if ((questionModel.getQuestionParts() != null) &&
                                    (questionModel.getQuestionParts().size() > 0)) {

                                for (QuestionPartModel questionPartModel : questionModel.getQuestionParts()) {

                                    if (questionPartModel.getQuestionPartMultiID().equalsIgnoreCase(questionMultiPartID)) {

                                        if (!CommonFunction.isEmptyStringField(questionPartModel.getQuestionPartDescription())) {

                                            HashMap< String, Object > questionPartMap = (HashMap< String, Object >) questionMap.get(questionPartModel.getQuestionPartMultiID());

                                            //mo, validation happens in below function
                                            if (questionModel.isQuestionMulti() && questionPartModel.getQuestionPartID().equalsIgnoreCase(questionMultiPartID)) {
                                                continue;
                                            }

                                            if (questionPartMap != null) {
                                                questionAnswerModels.addAll(generateQuestionAnswerModelForQuestionPart(questionModel, questionPartModel, questionPartMap, questionContainer));

                                            }
                                        }
                                    }
                                }

                            } else {
                                //mo, validation happens in below function
                                HashMap< String, Object > questionPartMap = (HashMap< String, Object >) questionMap.get(questionMultiPartID);
                                long start = System.currentTimeMillis();
                                questionAnswerModels.addAll(generateQuestionAnswerModelForQuestionPart(questionModel, null, Objects.requireNonNull(questionPartMap), questionContainer));
                                long end = System.currentTimeMillis();
                               System.out.println("Time elapsed generateQuestionAnswerModelForQuestionPart : "+(end-start));
                            }

                        }
                    }

//                }

                if (!skippedQ.contains(questionModel.getQuestionID())) {
                    skippedQ.add(questionModel.getQuestionID());
                }
            } else {
                popFragment();
            }
        }

        return questionAnswerModels;
    }


    //Called by parent func, generateAllQuestionAnswerModel()
    private RealmList< QuestionAnswerModel > generateQuestionAnswerModelForQuestionPart(QuestionModel questionModel, QuestionPartModel questionPartModels, HashMap< String, Object > parentMap, ViewGroup questionContainer) {

        RealmList< QuestionAnswerModel > questionAnswerModels = new RealmList<>();

        if ((parentMap.containsKey(FORM_LAYOUT_UI_CONTAINER_OBJECT_KEY)) &&
                (questionModel.getQuestionMeasurements() != null) &&
                (questionModel.getQuestionMeasurements().size() > 0)) {
            View questionPartContainer = (View) parentMap.get(FORM_LAYOUT_UI_CONTAINER_OBJECT_KEY);

            for (QuestionMeasurementModel questionMeasurementModel : questionModel.getQuestionMeasurements()) {
                if (!CommonFunction.isEmptyStringField(questionMeasurementModel.getQuestionMeasurementDescription())) {
                    HashMap< String, Object > measurementMap = (HashMap< String, Object >) parentMap.get(questionMeasurementModel.getQuestionMeasurementID());

                    //create question model
                    QuestionAnswerModel questionAnswerModel = new QuestionAnswerModel();
                    questionAnswerModel.setQuestionAnswerTaskID(taskID);
                    questionAnswerModel.setFlip(questionModel.getFlip());
                    questionAnswerModel.setQuestionAnswerFormID(formModel.getFormID());
                    questionAnswerModel.setQuestionAnswerQuestionID(questionModel.getQuestionID());
                    questionAnswerModel.setQuestionAnswerIsComment(false);
                    questionAnswerModel.setQuestionAnswerCommentTypeID(null);

                    if (questionPartModels != null) {
                        questionAnswerModel.setQuestionAnswerQuestionPartID(questionPartModels.getQuestionPartID());
                        questionAnswerModel.setQuestionAnswerQuestionPartMultiID(questionPartModels.getQuestionPartMultiID());

                    }

                    questionAnswerModel.setQuestionAnswerMeasurementID(questionMeasurementModel.getQuestionMeasurementID());
                    questionAnswerModel.setQuestionAnswerMeasurementTypeID(questionMeasurementModel.getQuestionMeasurementTypeID());

                    if (measurementMap != null) {
                        // Generate the values
                        if ((measurementMap.containsKey(FORM_LAYOUT_UI_CONTAINER_OBJECT_KEY)) &&
                                (measurementMap.containsKey(FORM_LAYOUT_UI_INPUT_OBJECT_KEY)) &&
                                (measurementMap.containsKey(FORM_LAYOUT_UI_ERROR_MESSAGE_OBJECT_KEY))) {

                            View measurementContainer = (View) measurementMap.get(FORM_LAYOUT_UI_CONTAINER_OBJECT_KEY);

                            //mo, init error message as empty >> will be filled when error occurs
                            TextView errorMessage_textView = (TextView) measurementMap.get(FORM_LAYOUT_UI_ERROR_MESSAGE_OBJECT_KEY);
                            Objects.requireNonNull(errorMessage_textView).setText("");
                            errorMessage_textView.setVisibility(View.GONE);

                            //-------------------------
                            // 1. MEASUREMENT VALIDATION
                            //-------------------------
                            if ((questionMeasurementModel.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_DECIMAL_TYPE_ID_KEY)) ||
                                    (questionMeasurementModel.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_TEXT_TYPE_ID_KEY)) ||
                                    (questionMeasurementModel.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_DATE_TYPE_ID_KEY)) ||
                                    (questionMeasurementModel.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_WHOLE_NUMBER_TYPE_ID_KEY))
                            ) {

                                //----------------
                                // TEXT BOX CONTROLS (a)(b)(c)
                                //----------------

                                EditText editText = (EditText) measurementMap.get(FORM_LAYOUT_UI_INPUT_OBJECT_KEY);

                                if ((questionContainer.getVisibility() == View.VISIBLE) &&
                                        (Objects.requireNonNull(questionPartContainer).getVisibility() == View.VISIBLE) &&
                                        (Objects.requireNonNull(measurementContainer).getVisibility() == View.VISIBLE)) {

                                    String inputValue = Objects.requireNonNull(editText).getText().toString();

                                    questionAnswerModel.setQuestionAnswerMeasurementOptionID(null);
                                    questionAnswerModel.setQuestionAnswerMeasurementOptionIDs(null);
                                    questionAnswerModel.setQuestionAnswerMeasurementTextResult(inputValue);



                                    if (questionMeasurementModel.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_DECIMAL_TYPE_ID_KEY)) {
                                        if (CommonFunction.isEmptyStringField(inputValue)) {
                                            questionAnswerModel.setQuestionAnswerMeasurementTextResult(null);
                                        } else {
                                            try {
                                                double doubleValue = Double.parseDouble(inputValue);
                                            } catch (Exception e) {
                                                questionAnswerModel.setQuestionAnswerMeasurementTextResult(null);
                                            }
                                        }
                                        //949801
                                        //952396

                                    } else if (questionMeasurementModel.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_WHOLE_NUMBER_TYPE_ID_KEY)) {
                                        if (CommonFunction.isEmptyStringField(inputValue)) {
                                            //nirvik, if whole number question type is not answered
                                            questionAnswerModel.setQuestionAnswerMeasurementTextResult(null);
                                        } else {
                                            try {
                                                int integerValue = Integer.parseInt(inputValue);
                                            } catch (Exception e) {
                                                questionAnswerModel.setQuestionAnswerMeasurementTextResult(null);
                                            }
                                        }
                                    }

                                    // Validate answer
                                    for (QuestionMeasurementValidationModel validationModel : questionMeasurementModel.getQuestionMeasurementValidations()) {
                                        if (errorMessage_textView.getVisibility() == View.GONE) {

                                            if ((validationModel.getMeasurementValidationID().equalsIgnoreCase(QuestionMeasurementValidationModel.QUESTION_MEASUREMENT_REQUIRED_VALIDATION_TYPE_ID_KEY)) &&
                                                    (validationModel.isMeasurementValidationIsRequired())) {

                                                // (a) REQUIRED FIELD VALIDATION
                                                if ((!isMidFormSaving) && (errorMessage_textView.getVisibility() == View.GONE) && (CommonFunction.isEmptyStringField(inputValue))) {
                                                    isAnswerValidated = false;
                                                    questionAnswerModel.setQuestionPartValidated(false);
                                                    errorMessage_textView.setText(validationModel.getMeasurementValidationErrorMessage());
                                                    errorMessage_textView.setVisibility(View.VISIBLE);
                                                }
                                            } else if ((validationModel.getMeasurementValidationID().equalsIgnoreCase(QuestionMeasurementValidationModel.QUESTION_MEASUREMENT_RANGE_VALIDATION_TYPE_ID_KEY)) &&
                                                    (!CommonFunction.isEmptyStringField(validationModel.getMeasurementValidationRange()))) {

                                                // (b) RANGE FIELD VALIDATION
                                                String[] ranges = validationModel.getMeasurementValidationRange().split("\\|");

                                                if (ranges.length == 2) {
                                                    try {
                                                        int minRange = Integer.parseInt(ranges[0]);
                                                        int maxRange = Integer.parseInt(ranges[1]);
                                                        int value = Integer.parseInt(inputValue);

                                                        //mo, 23/10/18, range validator >> minValue, maxValue excluded from comparison
                                                        if ((!isMidFormSaving) && (errorMessage_textView.getVisibility() == View.GONE) && ((value < minRange) || (value > maxRange))) {
                                                            isAnswerValidated = false;
                                                            questionAnswerModel.setQuestionPartValidated(false);
                                                            errorMessage_textView.setText(validationModel.getMeasurementValidationErrorMessage());
                                                            errorMessage_textView.setVisibility(View.VISIBLE);
                                                        }

                                                    } catch (Exception e) {
                                                        CommonFunction.print("error!");
                                                    }
                                                }
                                            } else if ((validationModel.getMeasurementValidationID().equalsIgnoreCase(QuestionMeasurementValidationModel.QUESTION_MEASUREMENT_EXPRESSION_VALIDATION_TYPE_ID_KEY)) &&
                                                    (!CommonFunction.isEmptyStringField(validationModel.getMeasurementValidationExpression()))) {

                                                // (c) REGULAR EXPRESSION VALIDATION

                                                String pattern = validationModel.getMeasurementValidationExpression();

                                                Pattern regexPattern = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                                                Matcher regexMatcher = regexPattern.matcher(inputValue);

                                                //mo, 25/8/17 apply date format validation only when user entered some value
                                                if (!CommonFunction.isEmptyStringField(inputValue)) {

                                                    if ((!isMidFormSaving) && (errorMessage_textView.getVisibility() == View.GONE) && (!regexMatcher.find())) {
                                                        isAnswerValidated = false;
                                                        questionAnswerModel.setQuestionPartValidated(false);
                                                        errorMessage_textView.setText(validationModel.getMeasurementValidationErrorMessage());
                                                        errorMessage_textView.setVisibility(View.VISIBLE);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    questionAnswerModel.setQuestionAnswerMeasurementOptionID(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_VISIBLE_OPTION_ID_VALUE);
                                    questionAnswerModel.setQuestionAnswerMeasurementOptionIDs(null);
                                    questionAnswerModel.setQuestionAnswerMeasurementTextResult(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_VISIBLE_TEXT_RESULT);
                                }
                            } else if ((questionMeasurementModel.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_DROPDOWN_TYPE_ID_KEY)) ||
                                    (questionMeasurementModel.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_META_TAG_DROPDOWN_TYPE_ID_KEY))) {

                                //----------------
                                // DROP DOWN LIST CONTROLS (a)
                                //----------------
                                Spinner spinner = (Spinner) measurementMap.get(FORM_LAYOUT_UI_INPUT_OBJECT_KEY);


                                if ((questionContainer.getVisibility() == View.VISIBLE) &&
                                        (Objects.requireNonNull(questionPartContainer).getVisibility() == View.VISIBLE) &&
                                        (Objects.requireNonNull(measurementContainer).getVisibility() == View.VISIBLE)) {

                                    if (Objects.requireNonNull(spinner).getSelectedItemPosition() != 0) {
                                        questionAnswerModel.setQuestionAnswerMeasurementOptionID(questionMeasurementModel.getQuestionMeasurementOptions().get(spinner.getSelectedItemPosition() - 1).getMeasurementOptionID());
                                        questionAnswerModel.setQuestionAnswerMeasurementOptionIDs(null);
                                        questionAnswerModel.setQuestionAnswerMeasurementTextResult((String) spinner.getSelectedItem());
                                    } else {
                                        questionAnswerModel.setQuestionAnswerMeasurementOptionID(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_ANSWERED_OPTION_ID_VALUE);
                                        questionAnswerModel.setQuestionAnswerMeasurementOptionIDs(null);
                                        questionAnswerModel.setQuestionAnswerMeasurementTextResult(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_ANSWERED_TEXT_RESULT);
                                    }

                                    // Validate answer
                                    for (QuestionMeasurementValidationModel validationModel : questionMeasurementModel.getQuestionMeasurementValidations()) {
                                        if ((validationModel.getMeasurementValidationID().equalsIgnoreCase(QuestionMeasurementValidationModel.QUESTION_MEASUREMENT_REQUIRED_VALIDATION_TYPE_ID_KEY)) &&
                                                (validationModel.isMeasurementValidationIsRequired())) {

                                            // (a) REQUIRED FIELD VALIDATION
                                            if ((!isMidFormSaving) && (spinner.getSelectedItemPosition() == 0)) {
                                                isAnswerValidated = false;
                                                questionAnswerModel.setQuestionPartValidated(false);
                                                errorMessage_textView.setText(validationModel.getMeasurementValidationErrorMessage());
                                                errorMessage_textView.setVisibility(View.VISIBLE);
                                            } else if(formModel.getFormType() == 12){
                                                if(!questionMeasurementModel.getQuestionMeasurementOptions().get(spinner.getSelectedItemPosition() - 1).isAnswer()){
                                                    isAnswerValidated = false;
                                                    questionAnswerModel.setQuestionPartValidated(false);
                                                    errorMessage_textView.setText("You have selected wrong answer");
                                                    errorMessage_textView.setVisibility(View.VISIBLE);
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    questionAnswerModel.setQuestionAnswerMeasurementOptionID(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_VISIBLE_OPTION_ID_VALUE);
                                    questionAnswerModel.setQuestionAnswerMeasurementOptionIDs(null);
                                    questionAnswerModel.setQuestionAnswerMeasurementTextResult(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_VISIBLE_TEXT_RESULT);
                                }
                            } else if (questionMeasurementModel.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_CHECKBOX_TYPE_ID_KEY)) {
                                //----------------
                                // CHECKBOX CONTROLS
                                //----------------
                                ImageView imageView = (ImageView) measurementMap.get(FORM_LAYOUT_UI_INPUT_OBJECT_KEY);

                                if ((questionContainer.getVisibility() == View.VISIBLE) &&
                                        (Objects.requireNonNull(questionPartContainer).getVisibility() == View.VISIBLE) &&
                                        (Objects.requireNonNull(measurementContainer).getVisibility() == View.VISIBLE)) {

                                    questionAnswerModel.setQuestionAnswerMeasurementOptionID((String) Objects.requireNonNull(imageView).getTag());
                                    questionAnswerModel.setQuestionAnswerMeasurementOptionIDs(null);
                                    questionAnswerModel.setQuestionAnswerMeasurementTextResult((String) imageView.getTag());
                                } else {
                                    questionAnswerModel.setQuestionAnswerMeasurementOptionID(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_VISIBLE_OPTION_ID_VALUE);
                                    questionAnswerModel.setQuestionAnswerMeasurementOptionIDs(null);
                                    questionAnswerModel.setQuestionAnswerMeasurementTextResult(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_VISIBLE_TEXT_RESULT);
                                }
                            } else if (questionMeasurementModel.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_MULTI_SELECT_TYPE_ID_KEY)) {
                                //----------------
                                // MULTI SELECT CONTROLS (a)
                                //----------------
                                TextView textView = (TextView) measurementMap.get(FORM_LAYOUT_UI_INPUT_OBJECT_KEY);

                                if ((questionContainer.getVisibility() == View.VISIBLE) &&
                                        (Objects.requireNonNull(questionPartContainer).getVisibility() == View.VISIBLE) &&
                                        (Objects.requireNonNull(measurementContainer).getVisibility() == View.VISIBLE)) {

                                    if (!Objects.requireNonNull(textView).getText().toString().equalsIgnoreCase(getString(R.string.MultiSelectionDropDownHintText)) &&
                                            !CommonFunction.isEmptyStringField(textView.getText().toString()) &&
                                            !textView.getText().toString().isEmpty()) {

                                        String stringValue = textView.getText().toString();
                                        stringValue = stringValue.replace(getString(R.string.MultiSelectionDropDownHintText), "");

                                        ArrayList< String > optionIDs = new ArrayList<>();
                                        ArrayList< String > selectedValues = new ArrayList<>();
                                        selectedValues.addAll(Arrays.asList(stringValue.split("\\|")));

                                        for (QuestionMeasurementOptionModel questionMeasurementOptionModel : questionMeasurementModel.getQuestionMeasurementOptions()) {
                                            if (selectedValues.contains(questionMeasurementOptionModel.getMeasurementOptionDescription())) {
                                                optionIDs.add(questionMeasurementOptionModel.getMeasurementOptionID());
                                            }
                                        }

                                        questionAnswerModel.setQuestionAnswerMeasurementOptionID(null);
                                        questionAnswerModel.setQuestionAnswerMeasurementOptionIDs(CommonFunction.combineArrayListIntoString(optionIDs, ","));
                                        questionAnswerModel.setQuestionAnswerMeasurementTextResult(stringValue);
                                    } else {
                                        questionAnswerModel.setQuestionAnswerMeasurementOptionID(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_ANSWERED_OPTION_ID_VALUE);
                                        questionAnswerModel.setQuestionAnswerMeasurementOptionIDs(null);
                                        questionAnswerModel.setQuestionAnswerMeasurementTextResult(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_ANSWERED_TEXT_RESULT);
                                    }

                                    // Validate answer
                                    for (QuestionMeasurementValidationModel validationModel : questionMeasurementModel.getQuestionMeasurementValidations()) {
                                        if ((validationModel.getMeasurementValidationID().equalsIgnoreCase(QuestionMeasurementValidationModel.QUESTION_MEASUREMENT_REQUIRED_VALIDATION_TYPE_ID_KEY)) &&
                                                (validationModel.isMeasurementValidationIsRequired())) {

                                            // (a) REQUIRED FIELD VALIDATION
                                            if ((!isMidFormSaving && (textView.getText().toString().equalsIgnoreCase(getString(R.string.MultiSelectionDropDownHintText))) || (textView.getText().toString().isEmpty()))) {
                                                isAnswerValidated = false;
                                                questionAnswerModel.setQuestionPartValidated(false);
                                                errorMessage_textView.setText(validationModel.getMeasurementValidationErrorMessage());
                                                errorMessage_textView.setVisibility(View.VISIBLE);
                                            } else if (formModel.getFormType() == 12) {
                                                boolean result = ValidationUtils.areAllCorrectAnswersSelected(questionMeasurementModel,textView.getText().toString());
                                                if(!result){
                                                    isAnswerValidated = false;
                                                    questionAnswerModel.setQuestionPartValidated(false);
                                                    errorMessage_textView.setText("Not all answers are correct.");
                                                    errorMessage_textView.setVisibility(View.VISIBLE);
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    questionAnswerModel.setQuestionAnswerMeasurementOptionID(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_VISIBLE_OPTION_ID_VALUE);
                                    questionAnswerModel.setQuestionAnswerMeasurementOptionIDs(null);
                                    questionAnswerModel.setQuestionAnswerMeasurementTextResult(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_VISIBLE_TEXT_RESULT);
                                }
                            }

                            //-------------------------
                            // 2. PHOTO VALIDATION (on m)
                            //-------------------------
                            if ((questionContainer.getVisibility() == View.VISIBLE) &&
                                    (Objects.requireNonNull(questionPartContainer).getVisibility() == View.VISIBLE) &&
                                    (Objects.requireNonNull(measurementContainer).getVisibility() == View.VISIBLE)) {

                                ImageView camera_m = measurementContainer.findViewById(R.id.camera_imageView);
                                validateYellowCamera(camera_m, questionModel, questionPartModels, questionMeasurementModel);

                            }
                        }

                        CommonFunction.print("SKIPPED QUESTIONS SAVING : CCA X " + questionAnswerModel.getQuestionAnswerQuestionID() + " : " + questionAnswerModel.getQuestionAnswerMeasurementTextResult());
                        questionAnswerModels.add(questionAnswerModel);
                    }
                }
            }
        }

        return questionAnswerModels;
    }

    //mo created, 23/5/18
    //Called by 2 places: q level and qm level
    private void validateYellowCamera(ImageView camera, QuestionModel questionModel, QuestionPartModel questionPartModel, QuestionMeasurementModel questionMeasurementModel) {
        //how do i know the yellow camera icon is showing?

        if (camera != null && camera.getVisibility() == View.VISIBLE && camera.getTag() != null) {

            //https://stackoverflow.com/questions/10726519/how-to-get-the-source-of-imageview-in-order-to-change-it
            int drawId = (Integer) camera.getTag();
            if (drawId == R.drawable.icon_camera_yellow) {
                isPhotoValidated = false;
            }
        }
        validateMeasurementPhoto(questionModel, questionPartModel, questionMeasurementModel);
    }

    private void validateMeasurementPhoto(QuestionModel questionModel, QuestionPartModel questionPartModel, QuestionMeasurementModel questionMeasurementModel) {

        GalleryInfo galleryInfo = DatabaseManager.getInstance(getContext()).createSectionViewArrayThree(ActivityBase.mRealm, taskID, formID, questionModel.getQuestionID(), questionPartModel.getQuestionPartID(), questionMeasurementModel.getQuestionMeasurementID(), questionPartModel.getQuestionPartMultiID());

        for (GallerySection gallerySection : galleryInfo.getSectionViewArray()) {

            int currentNum = gallerySection.getItems().size() - 1;   //-1 means 'add_image_button'
            int reqNum = gallerySection.getNumberOfPhotos();
            boolean bFinished = false;

            if (currentNum >= reqNum) {
                bFinished = true;
            } else {
                if (currentNum > 0) {
                    if (gallerySection.hasPhotoUnableToLoad()) {
                        bFinished = true;
                    }
                }
            }

            if (!bFinished && gallerySection.isMandatory()) {
                //Not all photos accounted for
                isPhotoValidated = false;
            }

        }
    }

    private ViewGroup populateQuestionHeader(ViewGroup parentView, final QuestionModel model) {
        View questionHeaderContainer_linearLayout = LayoutInflater.from(rootActivity).inflate(R.layout.question_header_item_v2, parentView, false);
        TextView questionHeaderTitle_textView = (TextView) ((ViewGroup) ((ViewGroup) questionHeaderContainer_linearLayout).getChildAt(1)).getChildAt(0); //ControlParser.parseControl(questionHeaderContainer_linearLayout, R.id.questionHeaderTitle_textView);
        ImageView questionHeaderPhoto_imageView = (ImageView) ((ViewGroup) ((ViewGroup) questionHeaderContainer_linearLayout).getChildAt(1)).getChildAt(1); //ControlParser.parseControl(questionHeaderContainer_linearLayout, R.id.questionHeaderPhoto_imageView);
        ImageView questionHeaderNote_imageView = (ImageView) ((ViewGroup) ((ViewGroup) questionHeaderContainer_linearLayout).getChildAt(1)).getChildAt(2); //ControlParser.parseControl(questionHeaderContainer_linearLayout, R.id.questionHeaderNote_imageView);
        ImageView camera = (ImageView) ((ViewGroup) ((ViewGroup) questionHeaderContainer_linearLayout).getChildAt(1)).getChildAt(3);

        ImageView questionMandatoryIndicator_imageview = (ImageView) ((ViewGroup) ((ViewGroup) questionHeaderContainer_linearLayout).getChildAt(1)).getChildAt(4);
        ImageView completeIndicator_imageView = (ImageView) ((ViewGroup) ((ViewGroup) questionHeaderContainer_linearLayout).getChildAt(1)).getChildAt(5);





        searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() {
            @Override
            public boolean onQueryTextSubmit(String query) {
                if (query.isEmpty()) {
                    // Reset the text if the query string is empty
                    resetHighlightedText();
                } else {
                    // Search for matches
                    List<TextView> matches = searchInLinearLayout(taskFormQuestionContainer_linearLayout, query);
                    if (matches.size() > 0) {
                        // Highlight the matching text
                        resetHighlightedText();
                        for (TextView match : matches) {
                            int start = match.getText().toString().toLowerCase(Locale.getDefault()).indexOf(query);
                            int end = start + query.length();
                            Spannable spannable = new SpannableString(match.getText());
                            BackgroundColorSpan span = new BackgroundColorSpan(Color.YELLOW);
                            spannable.setSpan(span, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                            match.setText(spannable);
                            highlightedViews.add(match);
                        }

                    } else {
                        // No matches found, reset highlighted text
                        resetHighlightedText();
                    }
                    int totalItems = highlightedViews.size();
                  searchItemCount.setText(String.valueOf(totalItems));
                    CommonFunction.hideSoftKeyboard(getActivity());

                }
                return true;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                return false;
            }
        });


        questionHeaderTitle_textView.setVisibility(View.GONE);
        questionHeaderPhoto_imageView.setVisibility(View.GONE);
        questionHeaderNote_imageView.setVisibility(View.GONE);
        camera.setVisibility(View.GONE);
        completeIndicator_imageView.setVisibility(View.GONE);
        questionMandatoryIndicator_imageview.setVisibility(View.GONE);

        parentView.addView(questionHeaderContainer_linearLayout);

        HashMap< String, Object > layoutMap = new HashMap<>();
        layoutMap.put(FORM_LAYOUT_UI_CONTAINER_OBJECT_KEY, questionHeaderContainer_linearLayout);


        //fill form hashmap with each question layout.
        formLayout_hashMap.put(model.getQuestionID(), layoutMap);


        return (ViewGroup) questionHeaderContainer_linearLayout;
    }



    private void resetHighlightedText() {
        // Reset any highlighted text back to normal
        for (TextView view : highlightedViews) {
            Spannable spannable = new SpannableString(view.getText());
            BackgroundColorSpan[] spans = spannable.getSpans(0, spannable.length(), BackgroundColorSpan.class);
            for (BackgroundColorSpan span : spans) {
                int start = spannable.getSpanStart(span);
                int end = spannable.getSpanEnd(span);
                spannable.removeSpan(span);
                span = new BackgroundColorSpan(Color.TRANSPARENT);
                spannable.setSpan(span, start, end, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
            }
            view.setText(spannable);
        }
        highlightedViews.clear();
    }


    private ViewGroup populateQuestionSubHeader(ViewGroup parentView, final QuestionPartModel questPart) {

        View questionSubHeaderContainer_linearLayout = LayoutInflater.from(rootActivity).inflate(R.layout.question_sub_header_supplementary_item, parentView, false);

        LinearLayout headerView = (LinearLayout) ((ViewGroup) questionSubHeaderContainer_linearLayout).getChildAt(0);
        LinearLayout middleView = (LinearLayout) ((ViewGroup) questionSubHeaderContainer_linearLayout).getChildAt(1);

        middleView.setVisibility(View.GONE);
        headerView.setVisibility(View.GONE);

        parentView.addView(questionSubHeaderContainer_linearLayout);
        ViewGroup question_linerLayout_Container = questionSubHeaderContainer_linearLayout.findViewById(R.id.question_linerLayout_Container);
        HashMap< String, Object > layoutMap = new HashMap<>();
        layoutMap.put(FORM_LAYOUT_UI_CONTAINER_OBJECT_KEY, question_linerLayout_Container);

        HashMap< String, Object > parentMap = (HashMap< String, Object >) CommonFunction.getParentMap(formLayout_hashMap, parentView);
        parentMap.put(questPart.getQuestionPartMultiID(), layoutMap);


        return (ViewGroup) questionSubHeaderContainer_linearLayout;

    }

    private void populateQuestionMeasuremet(ViewGroup parentView, QuestionModel questionModel, QuestionPartModel questionPartModel) {



        if ((questionModel.getQuestionMeasurements() != null)) {

            for (QuestionMeasurementModel questionMeasurementModel : questionModel.getQuestionMeasurements()) {

               // System.out.println("date? : "+questionMeasurementModel.isDateType());

                if (!CommonFunction.isEmptyStringField(questionMeasurementModel.getQuestionMeasurementDescription())) {

                    //mo, find user-selected-before answer according to taskid, fid, qid, qpid, mid. why is it null?
                    //mo, questionAnswerModel was null before saving form, but had value after saving form.
                    //mo, So, when a form is saved, Joshua reads user answer from questionAnswerModel, when a form is not saved, he reads answer from getQuestionMeasurementDefaultValue()

//                    CommonFunction.print("For qModel2: SAVE 9ZZZQA LOAD Q PQ " + questionModel.getQuestionID() + " : " + questionMeasurementModel.getQuestionMeasurementID() + " : " + questionMeasurementModel.getQuestionMeasurementDescription());

                    QuestionAnswerModel questionAnswerModel = DatabaseManager.getInstance(getContext()).getQuestionAnswerModelFromTaskIDWithFormID(ActivityBase.mRealm, taskID, formID, questionModel.getQuestionID(), (questionPartModel != null ? questionPartModel.getQuestionPartMultiID() : null), questionMeasurementModel.getQuestionMeasurementID(), true);

                        if (questionMeasurementModel.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_DATE_TYPE_ID_KEY)){
                            //----------------------------
                            // (a) Decimal type/Text/Number measurement
                            //----------------------------
                            String initialTextResult = "";


                            //mo, getQuestionMeasurementDefaultValue() was not null, but the right answer from user.
                            if (!CommonFunction.isEmptyStringField(questionMeasurementModel.getQuestionMeasurementDefaultValue())) {
                                initialTextResult = questionMeasurementModel.getQuestionMeasurementDefaultValue();
                            }

                            //mo, why questionAnswerModel was null when you come here? for q1, qp1, m1?
                            if ((questionAnswerModel != null) &&
                                    (questionAnswerModel.getQuestionAnswerMeasurementTypeID().equalsIgnoreCase(questionMeasurementModel.getQuestionMeasurementTypeID())) &&
                                    ((questionAnswerModel.getQuestionAnswerMeasurementOptionID() == null) ||
                                            ((questionAnswerModel.getQuestionAnswerMeasurementOptionID() != null) &&
                                                    (!questionAnswerModel.getQuestionAnswerMeasurementOptionID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_VISIBLE_OPTION_ID_VALUE)) &&
                                                    (!questionAnswerModel.getQuestionAnswerMeasurementOptionID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_ANSWERED_OPTION_ID_VALUE))))) {

                                if (!CommonFunction.isEmptyStringField(questionAnswerModel.getQuestionAnswerMeasurementTextResult())) {
                                    initialTextResult = questionAnswerModel.getQuestionAnswerMeasurementTextResult();
                                }
                            }

                            populateQuestionDateMeasurementType(parentView, questionMeasurementModel, initialTextResult, questionPartModel,questionModel);



                        }else if((questionMeasurementModel.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_DECIMAL_TYPE_ID_KEY)) ||
                                (questionMeasurementModel.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_TEXT_TYPE_ID_KEY))){

                            String initialTextResult = "";

                            if (!CommonFunction.isEmptyStringField(questionMeasurementModel.getQuestionMeasurementDefaultValue())) {
                                initialTextResult = questionMeasurementModel.getQuestionMeasurementDefaultValue();
                            }

                            if ((questionAnswerModel != null) &&
                                    (questionAnswerModel.getQuestionAnswerMeasurementTypeID().equalsIgnoreCase(questionMeasurementModel.getQuestionMeasurementTypeID())) &&
                                    ((questionAnswerModel.getQuestionAnswerMeasurementOptionID() == null) ||
                                            ((questionAnswerModel.getQuestionAnswerMeasurementOptionID() != null) &&
                                                    (!questionAnswerModel.getQuestionAnswerMeasurementOptionID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_VISIBLE_OPTION_ID_VALUE)) &&
                                                    (!questionAnswerModel.getQuestionAnswerMeasurementOptionID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_ANSWERED_OPTION_ID_VALUE))))) {

                                if (!CommonFunction.isEmptyStringField(questionAnswerModel.getQuestionAnswerMeasurementTextResult())) {

                                    initialTextResult = questionAnswerModel.getQuestionAnswerMeasurementTextResult();

//                                CommonFunction.print("QUESTION VALD : C " + questionAnswerModel.getQuestionAnswerMeasurementID() + " : " + initialTextResult);
                                }
                            }
                            populateQuestionEditTextMeasurementType(parentView, questionMeasurementModel, initialTextResult, questionPartModel, questionModel);



                        } else if ((questionMeasurementModel.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_WHOLE_NUMBER_TYPE_ID_KEY)) ){
                            String initialTextResult = "";

                            if (!CommonFunction.isEmptyStringField(questionMeasurementModel.getQuestionMeasurementDefaultValue())) {
                                initialTextResult = questionMeasurementModel.getQuestionMeasurementDefaultValue();
                            }

                            if ((questionAnswerModel != null) &&
                                    (questionAnswerModel.getQuestionAnswerMeasurementTypeID().equalsIgnoreCase(questionMeasurementModel.getQuestionMeasurementTypeID())) &&
                                    ((questionAnswerModel.getQuestionAnswerMeasurementOptionID() == null) ||
                                            ((questionAnswerModel.getQuestionAnswerMeasurementOptionID() != null) &&
                                                    (!questionAnswerModel.getQuestionAnswerMeasurementOptionID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_VISIBLE_OPTION_ID_VALUE)) &&
                                                    (!questionAnswerModel.getQuestionAnswerMeasurementOptionID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_ANSWERED_OPTION_ID_VALUE))))) {

                                if (!CommonFunction.isEmptyStringField(questionAnswerModel.getQuestionAnswerMeasurementTextResult())) {

                                    initialTextResult = questionAnswerModel.getQuestionAnswerMeasurementTextResult();

//                                CommonFunction.print("QUESTION VALD : C " + questionAnswerModel.getQuestionAnswerMeasurementID() + " : " + initialTextResult);
                                }
                            }
                            populateQuestionWholeNumberTextMeasurementType(parentView, questionMeasurementModel, initialTextResult, questionPartModel, questionModel);



                        } else if ((questionMeasurementModel.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_DROPDOWN_TYPE_ID_KEY)) ||
                            (questionMeasurementModel.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_META_TAG_DROPDOWN_TYPE_ID_KEY))) {

                        //----------------------------
                        // (b) ddl type measurement, metatag ddl
                        //----------------------------
                        String initialTextResult = "";

                        //mo, getQuestionMeasurementDefaultValue() was not null, but the right answer from user.
                        if (!CommonFunction.isEmptyStringField(questionMeasurementModel.getQuestionMeasurementDefaultValue())) {
                            initialTextResult = questionMeasurementModel.getQuestionMeasurementDefaultValue();
                        }

                        //mo, why questionAnswerModel was null when you come here? for q1, qp1, m1?
                        if ((questionAnswerModel != null) &&
                                (questionAnswerModel.getQuestionAnswerMeasurementTypeID().equalsIgnoreCase(questionMeasurementModel.getQuestionMeasurementTypeID())) &&
                                ((questionAnswerModel.getQuestionAnswerMeasurementOptionID() == null) ||
                                        ((questionAnswerModel.getQuestionAnswerMeasurementOptionID() != null) &&
                                                (!questionAnswerModel.getQuestionAnswerMeasurementOptionID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_VISIBLE_OPTION_ID_VALUE)) &&
                                                (!questionAnswerModel.getQuestionAnswerMeasurementOptionID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_ANSWERED_OPTION_ID_VALUE))))) {

                            if (!CommonFunction.isEmptyStringField(questionAnswerModel.getQuestionAnswerMeasurementTextResult())) {
                                initialTextResult = questionAnswerModel.getQuestionAnswerMeasurementTextResult();
                            }
                        }

                        //mo, whether questionAnswerModel is null or not, the measurement was created with a selected answer from initialTextResult.
                        populateQuestionDropdownMeasurementType(parentView, questionMeasurementModel, initialTextResult, questionPartModel, questionModel);

                    } else if (questionMeasurementModel.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_CHECKBOX_TYPE_ID_KEY)) {
                        //----------------------------
                        // (c) checkbox type measurement
                        //----------------------------
                        String initialOptionID = QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_ANSWERED_OPTION_ID_VALUE;

                        if ((questionAnswerModel != null) && (questionAnswerModel.getQuestionAnswerMeasurementTypeID().equalsIgnoreCase(questionMeasurementModel.getQuestionMeasurementTypeID()))) {
                            initialOptionID = questionAnswerModel.getQuestionAnswerMeasurementOptionID();
                        }

                        populateQuestionCheckboxMeasurementType(parentView, questionMeasurementModel, initialOptionID, questionPartModel, questionModel);
                        if(questionModel.getFlip()){
                            searchView.setVisibility(View.VISIBLE);
                            searchItemCount.setVisibility(View.VISIBLE);
                            totalCheck.setVisibility(View.VISIBLE);
                            totalCheckTV.setVisibility(View.VISIBLE);
                        }
                    } else if (questionMeasurementModel.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_MULTI_SELECT_TYPE_ID_KEY)) {
                        //----------------------------
                        // (d) multiselect type measurement
                        //----------------------------
                        String initialTextResult = "";

                        if (!CommonFunction.isEmptyStringField(questionMeasurementModel.getQuestionMeasurementDefaultValue())) {
                            initialTextResult = questionMeasurementModel.getQuestionMeasurementDefaultValue();
                        }

                        if ((questionAnswerModel != null) &&
                                (questionAnswerModel.getQuestionAnswerMeasurementTypeID().equalsIgnoreCase(questionMeasurementModel.getQuestionMeasurementTypeID())) &&
                                ((questionAnswerModel.getQuestionAnswerMeasurementOptionID() == null) ||
                                        ((questionAnswerModel.getQuestionAnswerMeasurementOptionID() != null) &&
                                                (!questionAnswerModel.getQuestionAnswerMeasurementOptionID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_VISIBLE_OPTION_ID_VALUE)) &&
                                                (!questionAnswerModel.getQuestionAnswerMeasurementOptionID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_ANSWERED_OPTION_ID_VALUE))))) {

                            if (!CommonFunction.isEmptyStringField(questionAnswerModel.getQuestionAnswerMeasurementTextResult())) {
                                initialTextResult = questionAnswerModel.getQuestionAnswerMeasurementTextResult();
                            }

                            if (!CommonFunction.isEmptyStringField(initialTextResult)) {
                                ArrayList< String > initialValues = new ArrayList<>(Arrays.asList(initialTextResult.split("\\|")));

                                boolean initialValuesChecked = false;

                                for (String value : initialValues) {
                                    initialValuesChecked = false;

                                    for (QuestionMeasurementOptionModel questionMeasurementOptionModel : questionMeasurementModel.getQuestionMeasurementOptions()) {
                                        if (value.equalsIgnoreCase(questionMeasurementOptionModel.getMeasurementOptionDescription())) {
                                            initialValuesChecked = true;
                                            break;
                                        }
                                    }
                                }

                                if (!initialValuesChecked) {
                                    initialTextResult = "";
                                }
                            }
                        }

                        populateQuestionMultiSelectMeasurementType(parentView, questionMeasurementModel, initialTextResult, questionModel, questionPartModel);
                    }

                }
            }
        }

    }

    private void populateCommentEditTextInput(ViewGroup parentView, QuestionModel model, String initialTextResult) {
        View containerLinearLayout = LayoutInflater.from(rootActivity).inflate(R.layout.question_edit_text_measurement_item, parentView, false);
        ViewGroup relativeLayout = ((ViewGroup) ((ViewGroup) containerLinearLayout).getChildAt(0));
        ViewGroup ddlLinearLayoutMand = ((ViewGroup) ((ViewGroup) containerLinearLayout).getChildAt(1));

        ViewGroup ddlLinearLayout = (((ViewGroup) ((ViewGroup) ((ViewGroup) containerLinearLayout).getChildAt(1)).getChildAt(0)));

        //measurement text
        TextView questionEditTextMeasurementTitle_textView = (TextView) relativeLayout.getChildAt(0);
        EditText questionEditTextMeasurementInputValue_editText = (EditText) (ddlLinearLayout.getChildAt(0));



        ImageView mandatoryQIndicator = (ImageView) (ddlLinearLayoutMand.getChildAt(1));
        mandatoryQIndicator.setVisibility(View.GONE);

        if (model.isQuestionIsCommentMandatory()) {
            mandatoryQIndicator.setVisibility(View.VISIBLE);
        }



        TextView questionEditTextMeasurementErrorMessage_textView = (TextView) ((ViewGroup) containerLinearLayout).getChildAt(2);

        questionEditTextMeasurementTitle_textView.setVisibility(View.GONE);

        //questionEditTextMeasurementInputValue_editText.setInputType(InputType.TYPE_TEXT_FLAG_MULTI_LINE);

        questionEditTextMeasurementInputValue_editText.setText(initialTextResult);
        questionEditTextMeasurementInputValue_editText.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (!hasFocus) {
                    ((EditText) v).setText(CommonFunction.cleanRetrievedString(((EditText) v).getText().toString()));
                }
            }
        });

        questionEditTextMeasurementErrorMessage_textView.setText("");
        questionEditTextMeasurementErrorMessage_textView.setVisibility(View.GONE);

        parentView.addView(containerLinearLayout);

        HashMap<String, Object> layoutMap = new HashMap<>();
        layoutMap.put(FORM_LAYOUT_UI_CONTAINER_OBJECT_KEY, containerLinearLayout);
        layoutMap.put(FORM_LAYOUT_UI_INPUT_OBJECT_KEY, questionEditTextMeasurementInputValue_editText);
        layoutMap.put(FORM_LAYOUT_UI_ERROR_MESSAGE_OBJECT_KEY, questionEditTextMeasurementErrorMessage_textView);

        HashMap<String, Object> parentMap = (HashMap<String, Object>) CommonFunction.getParentMap(formLayout_hashMap, parentView);
        parentMap.put(QuestionModel.QUESTION_COMMENT_TEXT_INPUT_ID_VALUE, layoutMap);

    }

    private void populateQuestionDateMeasurementType(ViewGroup parentView, final QuestionMeasurementModel model,
                                                     String initialTextResult, QuestionPartModel qpModel, final QuestionModel questionModel) {
        View containerLinearLayout = LayoutInflater.from(rootActivity).inflate(R.layout.questionmeasurement_date_type, parentView, false);
      //  View containerLinearLayout = LayoutInflater.from(rootActivity).inflate(R.layout.question_edit_text_measurement_item_ph, parentView, false);
        ViewGroup relativeLayout = ((ViewGroup) ((ViewGroup) containerLinearLayout).getChildAt(0));
        ViewGroup ddlLinearLayoutMand = ((ViewGroup) ((ViewGroup) containerLinearLayout).getChildAt(1));

        ViewGroup ddlLinearLayout = (((ViewGroup) ((ViewGroup) ((ViewGroup) containerLinearLayout).getChildAt(1)).getChildAt(0)));

        //CONTROL FIND
        //image view
       ImageView itemImage_imageView = (ImageView) relativeLayout.getChildAt(0);
        itemImage_imageView.setVisibility(View.GONE);
//
//        String imageURL = null;
//
//        for (QuestionPartModel qp : questionModel.getQuestionParts()) {
//            if(!model.getMeasurementImage().equalsIgnoreCase("0")){
//                itemImage_imageView.setVisibility(View.VISIBLE);
//                imageURL = model.getMeasurementImage();
//            }else{
//                itemImage_imageView.setVisibility(View.GONE);
//
//            }
//
//        }
//        new CommonFunction.DownloadImageTask(itemImage_imageView).execute(imageURL);
//
//        String finalImageURL = imageURL;
//        itemImage_imageView.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                Bundle bundle = new Bundle();
//                bundle.putString("imageURL", finalImageURL);
//                replaceFragment(new ImageFragment(),bundle,FragmentBase.SLIDE_IN_FROM_RIGHT_ANIMATION_KEY);
//
//            }
//        });






        //measurement text
        TextView questionEditTextMeasurementTitle_textView = (TextView) relativeLayout.getChildAt(1);
        //camera image

        ImageView camera = (ImageView) relativeLayout.getChildAt(2);
        camera.setVisibility(View.GONE);

        EditText questionEditTextMeasurementInputValue_editText = (EditText) (ddlLinearLayout.getChildAt(0));  //ControlParser.parseControl(questionEditTextMeasurementTypeContainer_linearLayout, R.id.questionEditTextMeasurementInputValue_editText);

        ImageView mandatoryQIndicator = (ImageView) (ddlLinearLayoutMand.getChildAt(1));
        mandatoryQIndicator.setVisibility(View.GONE);
        boolean isMeasurementRequired = false;

        for (QuestionMeasurementValidationModel validationModel : model.getQuestionMeasurementValidations()) {

            isMeasurementRequired = validationModel.isMeasurementValidationIsRequired();
        }

        if (isMeasurementRequired) {
            mandatoryQIndicator.setVisibility(View.VISIBLE);
        }

        TextView questionEditTextMeasurementErrorMessage_textView = (TextView) ((ViewGroup) containerLinearLayout).getChildAt(2); //ControlParser.parseControl(questionEditTextMeasurementTypeContainer_linearLayout, R.id.questionEditTextMeasurementErrorMessage_textView);

        //CONTROL SET VALUES
        //show camera >> only when the measurement belongs to the first question part
        populateCameraButtonOnMeasurement(camera, model, qpModel, questionModel);

        questionEditTextMeasurementTitle_textView.setText(model.getQuestionMeasurementDescription());

//        if (model.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_DECIMAL_TYPE_ID_KEY)) {
//            questionEditTextMeasurementInputValue_editText.setInputType(InputType.TYPE_CLASS_NUMBER | InputType.TYPE_NUMBER_FLAG_DECIMAL);
//        } else if (model.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_TEXT_TYPE_ID_KEY)) {
//
//
//        } else if (model.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_WHOLE_NUMBER_TYPE_ID_KEY)) {
//            questionEditTextMeasurementInputValue_editText.setInputType(InputType.TYPE_CLASS_NUMBER);
//        }

        questionEditTextMeasurementInputValue_editText.setInputType(InputType.TYPE_NULL);

        questionEditTextMeasurementInputValue_editText.setText(initialTextResult);

        questionEditTextMeasurementErrorMessage_textView.setText("");
        questionEditTextMeasurementErrorMessage_textView.setVisibility(View.GONE);

        questionEditTextMeasurementInputValue_editText.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Calendar calendar = Calendar.getInstance();
                int year = calendar.get(Calendar.YEAR);
                int month = calendar.get(Calendar.MONTH);
                int day = calendar.get(Calendar.DAY_OF_MONTH);

                DatePickerDialog datePickerDialog = new DatePickerDialog(
                        getContext(),
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String monthStr = String.valueOf(month+1);
                                if (monthStr.length() == 1) {
                                    monthStr = "0" + monthStr;
                                }
                                String dayStr = String.valueOf(day);
                                if (dayStr.length() == 1) {
                                    dayStr = "0" + dayStr;
                                }
                                questionEditTextMeasurementInputValue_editText.setText(String.format("%s/%s/%d", dayStr,monthStr, year));
                            }
                        },
                        year,
                        month,
                        day
                );
                datePickerDialog.show();
            }
        });

        parentView.addView(containerLinearLayout);

        containerLinearLayout.setVisibility(View.VISIBLE);
        if (model.getQuestionMeasurementDefaultAction().equalsIgnoreCase(QuestionModel.QUESTION_ACTION_DISAPPEAR_KEY)) {
            containerLinearLayout.setVisibility(View.GONE);
        }

        HashMap< String, Object > layoutMap = new HashMap<>();
        layoutMap.put(FORM_LAYOUT_UI_CONTAINER_OBJECT_KEY, containerLinearLayout);
        layoutMap.put(FORM_LAYOUT_UI_INPUT_OBJECT_KEY, questionEditTextMeasurementInputValue_editText);
        layoutMap.put(FORM_LAYOUT_UI_ERROR_MESSAGE_OBJECT_KEY, questionEditTextMeasurementErrorMessage_textView);

        CommonFunction.print("For qModel2:6 " + formLayout_hashMap.size());
        HashMap< String, Object > parentMap = (HashMap< String, Object >) CommonFunction.getParentMap(formLayout_hashMap, parentView);
        parentMap.put(model.getQuestionMeasurementID(), layoutMap);



    }



    //get question measurements for each question
    private void populateQuestionEditTextMeasurementType(ViewGroup parentView, final QuestionMeasurementModel model,
                                                         String initialTextResult, QuestionPartModel qpModel, QuestionModel questionModel) {
        View containerLinearLayout = LayoutInflater.from(rootActivity).inflate(R.layout.question_edit_text_measurement_item_ph, parentView, false);
        ViewGroup relativeLayout = ((ViewGroup) ((ViewGroup) containerLinearLayout).getChildAt(0));
        ViewGroup ddlLinearLayoutMand = ((ViewGroup) ((ViewGroup) containerLinearLayout).getChildAt(1));

        ViewGroup ddlLinearLayout = (((ViewGroup) ((ViewGroup) ((ViewGroup) containerLinearLayout).getChildAt(1)).getChildAt(0)));

        //CONTROL FIND
        //image view
        ImageView itemImage_imageView = (ImageView) relativeLayout.getChildAt(0);
        itemImage_imageView.setVisibility(View.GONE);
//
//        String imageURL = null;
//
//        for (QuestionPartModel qp : questionModel.getQuestionParts()) {
//                if(!model.getMeasurementImage().equalsIgnoreCase("0")){
//                    itemImage_imageView.setVisibility(View.VISIBLE);
//                    imageURL = model.getMeasurementImage();
//                }else{
//
//
//            }
//
//        }
//        new CommonFunction.DownloadImageTask(itemImage_imageView).execute(imageURL);
//
//        String finalImageURL = imageURL;
//        itemImage_imageView.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                Bundle bundle = new Bundle();
//                bundle.putString("imageURL", finalImageURL);
//                replaceFragment(new ImageFragment(),bundle,FragmentBase.SLIDE_IN_FROM_RIGHT_ANIMATION_KEY);
//
//            }
//        });

        //measurement text
        TextView questionEditTextMeasurementTitle_textView = (TextView) relativeLayout.getChildAt(1);

        //camera image

        ImageView camera = (ImageView) relativeLayout.getChildAt(2);
        camera.setVisibility(View.GONE);

        EditText questionEditTextMeasurementInputValue_editText = (EditText) (ddlLinearLayout.getChildAt(0));  //ControlParser.parseControl(questionEditTextMeasurementTypeContainer_linearLayout, R.id.questionEditTextMeasurementInputValue_editText);

        //nirvik, for indies
//        questionEditTextMeasurementInputValue_editText.setOnEditorActionListener((v, actionId, event) -> {
//            if (actionId == 0) {
//                // Capture the "Done" or "Return" key press
//                // Add a new line character to the current text
//                questionEditTextMeasurementInputValue_editText.append("\n");
//                return true; // Consume the event, preventing the default action (moving to the next view)
//            }
//            return false; // Let the system handle other events
//        });
        ImageView mandatoryQIndicator = (ImageView) (ddlLinearLayoutMand.getChildAt(1));
        mandatoryQIndicator.setVisibility(View.GONE);
        boolean isMeasurementRequired = false;

        for (QuestionMeasurementValidationModel validationModel : model.getQuestionMeasurementValidations()) {

            isMeasurementRequired = validationModel.isMeasurementValidationIsRequired();
        }

        if (isMeasurementRequired) {
            mandatoryQIndicator.setVisibility(View.VISIBLE);
        }

        TextView questionEditTextMeasurementErrorMessage_textView = (TextView) ((ViewGroup) containerLinearLayout).getChildAt(2); //ControlParser.parseControl(questionEditTextMeasurementTypeContainer_linearLayout, R.id.questionEditTextMeasurementErrorMessage_textView);

        //CONTROL SET VALUES
        //show camera >> only when the measurement belongs to the first question part
        populateCameraButtonOnMeasurement(camera, model, qpModel, questionModel);

        questionEditTextMeasurementTitle_textView.setText(model.getQuestionMeasurementDescription());

        if (model.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_DECIMAL_TYPE_ID_KEY)) {
            questionEditTextMeasurementInputValue_editText.setInputType(InputType.TYPE_CLASS_NUMBER | InputType.TYPE_NUMBER_FLAG_DECIMAL);
        } else if (model.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_TEXT_TYPE_ID_KEY)) {
            questionEditTextMeasurementInputValue_editText.setInputType(InputType.TYPE_CLASS_TEXT);

        } else if (model.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_WHOLE_NUMBER_TYPE_ID_KEY)) {
            questionEditTextMeasurementInputValue_editText.setInputType(InputType.TYPE_CLASS_NUMBER);
        }

        questionEditTextMeasurementInputValue_editText.setText(initialTextResult);

        questionEditTextMeasurementErrorMessage_textView.setText("");
        questionEditTextMeasurementErrorMessage_textView.setVisibility(View.GONE);

        parentView.addView(containerLinearLayout);

        containerLinearLayout.setVisibility(View.VISIBLE);
        if (model.getQuestionMeasurementDefaultAction().equalsIgnoreCase(QuestionModel.QUESTION_ACTION_DISAPPEAR_KEY)) {
            containerLinearLayout.setVisibility(View.GONE);
        }

        HashMap< String, Object > layoutMap = new HashMap<>();
        layoutMap.put(FORM_LAYOUT_UI_CONTAINER_OBJECT_KEY, containerLinearLayout);
        layoutMap.put(FORM_LAYOUT_UI_INPUT_OBJECT_KEY, questionEditTextMeasurementInputValue_editText);
        layoutMap.put(FORM_LAYOUT_UI_ERROR_MESSAGE_OBJECT_KEY, questionEditTextMeasurementErrorMessage_textView);

        CommonFunction.print("For qModel2:6 " + formLayout_hashMap.size());
        HashMap< String, Object > parentMap = (HashMap< String, Object >) CommonFunction.getParentMap(formLayout_hashMap, parentView);
        parentMap.put(model.getQuestionMeasurementID(), layoutMap);

    }

    private void populateQuestionWholeNumberTextMeasurementType(ViewGroup parentView, final QuestionMeasurementModel model,
                                                         String initialTextResult, QuestionPartModel qpModel, QuestionModel questionModel) {
        View containerLinearLayout = LayoutInflater.from(rootActivity).inflate(R.layout.question_whole_number_measurement_item, parentView, false);
        ViewGroup relativeLayout = ((ViewGroup) ((ViewGroup) containerLinearLayout).getChildAt(0));
    //    ViewGroup ddlLinearLayoutMand = ((ViewGroup) ((ViewGroup) containerLinearLayout).getChildAt(1));


        //CONTROL FIND
        //image view
        ImageView itemImage_imageView = (ImageView) relativeLayout.getChildAt(0);
        itemImage_imageView.setVisibility(View.GONE);
//
//        String imageURL = null;
//
//        for (QuestionPartModel qp : questionModel.getQuestionParts()) {
//            if(!model.getMeasurementImage().equalsIgnoreCase("0")){
//                itemImage_imageView.setVisibility(View.VISIBLE);
//                imageURL = model.getMeasurementImage();
//            }else{
//                itemImage_imageView.setVisibility(View.GONE);
//
//            }
//
//        }
//        new CommonFunction.DownloadImageTask(itemImage_imageView).execute(imageURL);
//
//        String finalImageURL = imageURL;
//        itemImage_imageView.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                Bundle bundle = new Bundle();
//                bundle.putString("imageURL", finalImageURL);
//                replaceFragment(new ImageFragment(),bundle,FragmentBase.SLIDE_IN_FROM_RIGHT_ANIMATION_KEY);
//
//            }
//        });




        //measurement text
        TextView questionEditTextMeasurementTitle_textView = (TextView) relativeLayout.getChildAt(1);
        //camera image

        ImageView camera = (ImageView) relativeLayout.getChildAt(2);
        camera.setVisibility(View.GONE);

        ViewGroup ddlLinearLayout =(ViewGroup) relativeLayout.getChildAt(3);




        ImageView minusImageView = (ImageView) ddlLinearLayout.getChildAt(0);
        EditText questionEditTextMeasurementInputValue_editText = (EditText) (ddlLinearLayout.getChildAt(1));  //ControlParser.parseControl(questionEditTextMeasurementTypeContainer_linearLayout, R.id.questionEditTextMeasurementInputValue_editText);
        ImageView addImageView = (ImageView) ddlLinearLayout.getChildAt(2);

        //setting the questionEditText to 0 initially to avoid null
      //  questionEditTextMeasurementInputValue_editText.setText("0");

        minusImageView.setOnClickListener(v -> {
            String originalString = questionEditTextMeasurementInputValue_editText.getText().toString().trim();
            if(!originalString.isEmpty()){
                if(!originalString.equalsIgnoreCase("1")){
                    int original = Integer.parseInt(originalString);
                    original--;
                    questionEditTextMeasurementInputValue_editText.setText(String.valueOf(original));
                }else{
                    questionEditTextMeasurementInputValue_editText.setText("");
                }
            }


        });

        addImageView.setOnClickListener(v -> {
            String originalString = questionEditTextMeasurementInputValue_editText.getText().toString().trim();

            if(!originalString.isEmpty()){
                int original = Integer.parseInt(originalString);
                original++;
                questionEditTextMeasurementInputValue_editText.setText(String.valueOf(original));
            }else{
                questionEditTextMeasurementInputValue_editText.setText("1");
            }


        });

        ImageView mandatoryQIndicator = (ImageView) (ddlLinearLayout.getChildAt(3));
        mandatoryQIndicator.setVisibility(View.GONE);
        boolean isMeasurementRequired = false;

        for (QuestionMeasurementValidationModel validationModel : model.getQuestionMeasurementValidations()) {

            isMeasurementRequired = validationModel.isMeasurementValidationIsRequired();
        }

        if (isMeasurementRequired) {
            mandatoryQIndicator.setVisibility(View.VISIBLE);
        }

        TextView questionEditTextMeasurementErrorMessage_textView = (TextView) ((ViewGroup) containerLinearLayout).getChildAt(1); //ControlParser.parseControl(questionEditTextMeasurementTypeContainer_linearLayout, R.id.questionEditTextMeasurementErrorMessage_textView);

        //CONTROL SET VALUES
        //show camera >> only when the measurement belongs to the first question part
        populateCameraButtonOnMeasurement(camera, model, qpModel, questionModel);

        questionEditTextMeasurementTitle_textView.setText(model.getQuestionMeasurementDescription());

        if (model.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_DECIMAL_TYPE_ID_KEY)) {
            questionEditTextMeasurementInputValue_editText.setInputType(InputType.TYPE_CLASS_NUMBER | InputType.TYPE_NUMBER_FLAG_DECIMAL);
        } else if (model.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_TEXT_TYPE_ID_KEY)) {
            questionEditTextMeasurementInputValue_editText.setInputType(InputType.TYPE_CLASS_TEXT);

        } else if (model.getQuestionMeasurementTypeID().equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_WHOLE_NUMBER_TYPE_ID_KEY)) {
            questionEditTextMeasurementInputValue_editText.setInputType(InputType.TYPE_CLASS_NUMBER);
        }

        questionEditTextMeasurementInputValue_editText.setText(initialTextResult);

        questionEditTextMeasurementErrorMessage_textView.setText("");
        questionEditTextMeasurementErrorMessage_textView.setVisibility(View.GONE);

        parentView.addView(containerLinearLayout);

        containerLinearLayout.setVisibility(View.VISIBLE);
        if (model.getQuestionMeasurementDefaultAction().equalsIgnoreCase(QuestionModel.QUESTION_ACTION_DISAPPEAR_KEY)) {
            containerLinearLayout.setVisibility(View.GONE);
        }

        HashMap< String, Object > layoutMap = new HashMap<>();
        layoutMap.put(FORM_LAYOUT_UI_CONTAINER_OBJECT_KEY, containerLinearLayout);
        layoutMap.put(FORM_LAYOUT_UI_INPUT_OBJECT_KEY, questionEditTextMeasurementInputValue_editText);
        layoutMap.put(FORM_LAYOUT_UI_ERROR_MESSAGE_OBJECT_KEY, questionEditTextMeasurementErrorMessage_textView);

        CommonFunction.print("For qModel2:6 " + formLayout_hashMap.size());
        HashMap< String, Object > parentMap = (HashMap< String, Object >) CommonFunction.getParentMap(formLayout_hashMap, parentView);
        parentMap.put(model.getQuestionMeasurementID(), layoutMap);

    }


    //mo, create ddl measurement with selected answer (selected index, which was decided by initialTextResult)
    //extra parameter final qid, qp_index added, model changed to final.
    private void populateQuestionDropdownMeasurementType(ViewGroup parentView, final QuestionMeasurementModel model,
                                                         String initialTextResult, QuestionPartModel qpModel, final QuestionModel questionModel) {
        //question_dropdown_item_ph.xml
        View containerLinearLayout = LayoutInflater.from(rootActivity).inflate(R.layout.question_dropdown_measurement_item_ph, parentView, false);
        ViewGroup relativeLayout = ((ViewGroup) ((ViewGroup) containerLinearLayout).getChildAt(0));
        ViewGroup ddlLinearLayoutMand = ((ViewGroup) ((ViewGroup) containerLinearLayout).getChildAt(1));
        ViewGroup ddlLinearLayout = (((ViewGroup) ((ViewGroup) ((ViewGroup) containerLinearLayout).getChildAt(1)).getChildAt(0)));

        //CONTROL FIND
        //measurement text
        TextView questionDropdownMeasurementTitle_textView = (TextView) relativeLayout.getChildAt(0);
        //camera image

        ImageView camera = (ImageView) relativeLayout.getChildAt(1);
        camera.setVisibility(View.GONE);


        //CONTROL SET VALUES
        //show camera >> only when the measurement belongs to the first question part
        populateCameraButtonOnMeasurement(camera, model, qpModel, questionModel);

        //ddl
        Spinner questionDropdownMeasurementInputValue_spinner = (Spinner) ((ddlLinearLayout).getChildAt(0));  //ControlParser.parseControl(questionDropdownMeasurementTypeContainer_linearLayout, R.id.questionDropdownMeasurementInputValue_spinner);
        //validation
        TextView questionDropdownMeasurementErrorMessage_textView = (TextView) (((ViewGroup) containerLinearLayout).getChildAt(2));

        //mo, this is Measurement text
        questionDropdownMeasurementTitle_textView.setText(model.getQuestionMeasurementDescription());

        ImageView mandatoryQIndicator = (ImageView) (ddlLinearLayoutMand.getChildAt(1));
        mandatoryQIndicator.setVisibility(View.GONE);
        boolean isMeasurementRequired = false;

        for (QuestionMeasurementValidationModel validationModel : model.getQuestionMeasurementValidations()) {
            isMeasurementRequired = validationModel.isMeasurementValidationIsRequired();
        }

        if (isMeasurementRequired) {
            mandatoryQIndicator.setVisibility(View.VISIBLE);
        }

        int selectedIndex = 0;
        int spinnerSize = model.getQuestionMeasurementOptions().size() + 1;
        String[] spinnerValues = new String[spinnerSize];
        spinnerValues[0] = getString(R.string.SingleSelectionDropDownHintText);

        for (int i = 1; i < spinnerSize; i++) {
            QuestionMeasurementOptionModel objMO = model.getQuestionMeasurementOptions().get(i - 1);
            spinnerValues[i] = Objects.requireNonNull(objMO).getMeasurementOptionDescription();

            if (initialTextResult.equalsIgnoreCase(objMO.getMeasurementOptionDescription())) {
                selectedIndex = i;
            }
        }

        //mo, fill spinner with options
        SpinnerAdapter questionDropdownMeasurementInputValue_spinnerAdapter = new SpinnerAdapter(rootActivity, android.R.layout.simple_spinner_item, 13, true, spinnerValues);
        questionDropdownMeasurementInputValue_spinnerAdapter.setDropDownViewResource(R.layout.spinner_dropdown_item);
        questionDropdownMeasurementInputValue_spinner.setAdapter(questionDropdownMeasurementInputValue_spinnerAdapter);

        questionDropdownMeasurementInputValue_spinner.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                resetSpinner = true;
                if (event.getAction() == MotionEvent.ACTION_UP) {
                    requireView().requestFocus();
                    CommonFunction.hideSoftKeyboard(requireActivity());
                }
                return false;
            }
        });
        questionDropdownMeasurementInputValue_spinner.setSelection(0);

        questionDropdownMeasurementInputValue_spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {

            //----------------------------------
            // mo, mo selected (When user selected an answer from drop down list, you come here.)
            //NIRVIK, found a better solution?
            //----------------------------------
            @Override
            public void onItemSelected(AdapterView< ? > parent, View view, int position, long id) {


                if ((position > 0) && (view != null)) {

                    HashMap< String, Object > measurementMap = (HashMap< String, Object >) CommonFunction.getParentMap(formLayout_hashMap, view.getParent());
                    String mid = CommonFunction.getParentKey(formLayout_hashMap, measurementMap);

                    HashMap< String, Object > questionPartMap = (HashMap< String, Object >) CommonFunction.getParentMap(formLayout_hashMap, measurementMap);
                    String qpid = CommonFunction.getParentKey(formLayout_hashMap, questionPartMap);

                    HashMap< String, Object > questionMap = (HashMap< String, Object >) CommonFunction.getParentMap(formLayout_hashMap, questionPartMap);
                    String qid = CommonFunction.getParentKey(formLayout_hashMap, questionMap);


                    QuestionModel selectedQuestionModel = null;
                    QuestionMeasurementModel selectedQuestionMeasurementModel = null;

                    // If there is no question part
                    if(questionMap != null && formLayout_hashMap != null)
                    {
                        if (questionMap.equals(formLayout_hashMap)) {
                            qid = qpid;
                        }
                    }

                    if ((measurementMap != null) && (questionPartMap != null) && (questionMap != null) &&
                            (mid != null) && (qpid != null) && (qid != null) &&
                            (formModel.getFormQuestions() != null) && (formModel.getFormQuestions().size() > 0)) {

                        Iterator< QuestionModel > questionModelIterator = formModel.getFormQuestions().iterator();
                        while ((questionModelIterator.hasNext()) && (selectedQuestionModel == null)) {
                            QuestionModel questionModel = questionModelIterator.next();

                            if (questionModel.getQuestionID().equalsIgnoreCase(qid)) {
                                //(1) which q?
                                selectedQuestionModel = questionModel;

                            }
                        }

                        if (selectedQuestionModel != null) {

                            if ((selectedQuestionModel.getQuestionMeasurements() != null) && (selectedQuestionModel.getQuestionMeasurements().size() > 0)) {
                                Iterator< QuestionMeasurementModel > questionMeasurementIterator = selectedQuestionModel.getQuestionMeasurements().iterator();
                                while ((questionMeasurementIterator.hasNext()) && (selectedQuestionMeasurementModel == null)) {

                                    QuestionMeasurementModel questionMeasurementModel = questionMeasurementIterator.next();
                                    System.out.println("QuestionMeasurement Iterator :" + questionMeasurementModel.getQuestionMeasurementDescription());
                                    if (questionMeasurementModel.getQuestionMeasurementID().equalsIgnoreCase(mid)) {
                                        //(2) which m?
                                        selectedQuestionMeasurementModel = questionMeasurementModel;
                                    }
                                }

                                if (selectedQuestionMeasurementModel != null) {
                                    //(3) which mo? (selected mo)
                                    //nirvik, I come here when I select spinner
                                    QuestionMeasurementOptionModel questionMeasurementOptionModel = selectedQuestionMeasurementModel.getQuestionMeasurementOptions().get(position - 1);
                                    System.out.println("QuestionMeasurement OptionModel : "+ questionMeasurementOptionModel.getMeasurementOptionDescription());
//                                    String moid = Objects.requireNonNull(questionMeasurementOptionModel).getMeasurementOptionID();
//                                    String amount = questionMeasurementOptionModel.getBudgetOffset();

                                    //region #mll
                                    //mo, 13/2/17, Multiple question condition
                                    if (!selectedQuestionModel.isQuestionIsMLL()) {
                                        //------------------------------
                                        //mo, old question condition begin (question looping, existing measurement condition)
                                        //------------------------------
                                        if ((selectedQuestionMeasurementModel.
                                                getQuestionMeasurementConditions() != null) && (selectedQuestionMeasurementModel.getQuestionMeasurementConditions().size() > 0)) {

                                            //mo, loop through each condition item
                                            for (QuestionMeasurementConditionModel questionMeasurementConditionModel : selectedQuestionMeasurementModel.getQuestionMeasurementConditions()) {
                                                if ((questionMeasurementConditionModel.getMeasurementConditionMeasurementID().equalsIgnoreCase(mid)) &&
                                                        (questionMeasurementConditionModel.getMeasurementConditionOptionID().equalsIgnoreCase(questionMeasurementOptionModel.getMeasurementOptionID()))) {

                                                    //mo, if the condition item matches 'user selected answer' situation, apply the condition to target measurements.
                                                    String targetMeasurementID = questionMeasurementConditionModel.getMeasurementConditionActionMeasurementID();
                                                    String targetAction = questionMeasurementConditionModel.getMeasurementConditionAction();

                                                    //mo, create targetMeasurement hashmap and view, and show/hide the view.
                                                    if (questionPartMap.containsKey(targetMeasurementID)) {
                                                        HashMap< String, Object > targetMeasurementMap = (HashMap< String, Object >) questionPartMap.get(targetMeasurementID);

                                                        if (Objects.requireNonNull(targetMeasurementMap).containsKey(FORM_LAYOUT_UI_CONTAINER_OBJECT_KEY)) {
                                                            View m_targetView = (View) targetMeasurementMap.get(FORM_LAYOUT_UI_CONTAINER_OBJECT_KEY);

                                                            if (targetAction.equalsIgnoreCase(QuestionModel.QUESTION_ACTION_APPEAR_KEY)) {
                                                                if (m_targetView != null) {
                                                                    m_targetView.setVisibility(View.VISIBLE);
                                                                }
                                                                //    showTargetMeasurementHidden(Objects.requireNonNull(m_targetView));

                                                            } else if (targetAction.equalsIgnoreCase(QuestionModel.QUESTION_ACTION_DISAPPEAR_KEY)) {
//                                                                m_targetView.setVisibility(View.GONE);
                                                                hideTargetMeasurementShown(Objects.requireNonNull(m_targetView));

                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        //------------------------------
                                        // old question condition end
                                        //------------------------------
                                    } else {
                                        //------------------------------
                                        //mo, 13/2/17, is_mll begin
                                        //------------------------------
                                        if ((selectedQuestionMeasurementModel.getQuestionMeasurementConditionsMultiple() != null) && (selectedQuestionMeasurementModel.getQuestionMeasurementConditionsMultiple().size() > 0)) {

                                            //mo, loop through each condition item
                                            for (QuestionMeasurementConditionModel questionMeasurementConditionModel : selectedQuestionMeasurementModel.getQuestionMeasurementConditionsMultiple()) {

                                                if ((questionMeasurementConditionModel.getMeasurementConditionMeasurementID().equalsIgnoreCase(mid)) &&
                                                        (questionMeasurementConditionModel.getMeasurementConditionOptionID().equalsIgnoreCase(questionMeasurementOptionModel.getMeasurementOptionID()))) {

                                                    //mo, if the condition item matches 'user selected answer' situation, apply the condition to target measurements.
                                                    String targetMeasurementID = questionMeasurementConditionModel.getMeasurementConditionActionMeasurementID();
                                                    String targetAction = questionMeasurementConditionModel.getMeasurementConditionAction();

                                                    //mo, create targetMeasurement hashmap and view, and show/hide the view.
                                                    if (questionPartMap.containsKey(targetMeasurementID)) {
                                                        HashMap< String, Object > targetMeasurementMap = (HashMap< String, Object >) questionPartMap.get(targetMeasurementID);
                                                       // System.out.println("targetAction for :" + selectedQuestionMeasurementModel.getQuestionMeasurementDescription() + " question: " + selectedQuestionMeasurementModel.getQuestionMeasurementID() + " is " + targetAction + " " + targetMeasurementID);

                                                        if (Objects.requireNonNull(targetMeasurementMap).containsKey(FORM_LAYOUT_UI_CONTAINER_OBJECT_KEY)) {
                                                            View m_targetView = (View) targetMeasurementMap.get(FORM_LAYOUT_UI_CONTAINER_OBJECT_KEY);

                                                            if (targetAction.equalsIgnoreCase(QuestionModel.QUESTION_ACTION_APPEAR_KEY)) {
                                                                showTargetMeasurementHidden(Objects.requireNonNull(m_targetView));
                                                                //if the shown measurement is dropdown reset it
                                                              //  resetAnswer(targetMeasurementID);



                                                            } else if (targetAction.equalsIgnoreCase(QuestionModel.QUESTION_ACTION_DISAPPEAR_KEY)) {
                                                              hideTargetMeasurementShown(Objects.requireNonNull(m_targetView));

                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }

                                        //------------------------------
                                        //mo, 13/2/17, is_mll end
                                        //------------------------------

                                    }

                                    //region #question skipping
                                    if ((selectedQuestionModel.getQuestionConditions() != null) && (selectedQuestionModel.getQuestionConditions().size() > 0)) {

                                        //mo, for each question conditions of this q, find qc object for the selected measurement and measurement option, and decide what to show/hide.
                                        for (QuestionConditionModel questionConditionModel : selectedQuestionModel.getQuestionConditions()) {

                                            if (!allQuestionsIDs.contains(questionConditionModel.getQuestionConditionActionQuestionID())) {
                                                allQuestionsIDs.add(questionConditionModel.getQuestionConditionActionQuestionID());
                                            }

                                            if ((questionConditionModel.getQuestionConditionMeasurementID().equalsIgnoreCase(mid)) &&
                                                    (questionConditionModel.getQuestionConditionMeasurementOptionID().equalsIgnoreCase(questionMeasurementOptionModel.getMeasurementOptionID()))) {

                                                String targetQuestionID = questionConditionModel.getQuestionConditionActionQuestionID();
                                                String targetAction = questionConditionModel.getQuestionConditionAction();

                                                if (targetAction.equalsIgnoreCase(QuestionModel.QUESTION_ACTION_DISAPPEAR_KEY)) {
                                                    addQuestionState(skippedQuestions, targetQuestionID);

                                                    if (notSkippedQuestions.size() > 0) {
                                                        removeQuestionState(notSkippedQuestions, targetQuestionID);
                                                    }
                                                } else {
                                                    addQuestionState(notSkippedQuestions, targetQuestionID);
                                                    if (skippedQuestions.size() > 0) {
                                                        removeQuestionState(skippedQuestions, targetQuestionID);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            @Override
            public void onNothingSelected(AdapterView< ? > parent) {


            }
        }); //onItemSelected Listener finish

        questionDropdownMeasurementInputValue_spinner.setSelection(selectedIndex);

        questionDropdownMeasurementErrorMessage_textView.setText("");
        questionDropdownMeasurementErrorMessage_textView.setVisibility(View.GONE);

        parentView.addView(containerLinearLayout);

        containerLinearLayout.setVisibility(View.VISIBLE);

        if (model.getQuestionMeasurementDefaultAction().equalsIgnoreCase(QuestionModel.QUESTION_ACTION_DISAPPEAR_KEY)) {
            containerLinearLayout.setVisibility(View.GONE);
        }

        HashMap< String, Object > layoutMap = new HashMap<>();
        layoutMap.put(FORM_LAYOUT_UI_CONTAINER_OBJECT_KEY, containerLinearLayout);
        layoutMap.put(FORM_LAYOUT_UI_INPUT_OBJECT_KEY, questionDropdownMeasurementInputValue_spinner);
        layoutMap.put(FORM_LAYOUT_UI_ERROR_MESSAGE_OBJECT_KEY, questionDropdownMeasurementErrorMessage_textView);

        HashMap< String, Object > parentMap = (HashMap< String, Object >) CommonFunction.getParentMap(formLayout_hashMap, parentView);
        parentMap.put(model.getQuestionMeasurementID(), layoutMap);

    }

    private void resetAnswer(String targetMeasurementID) {

    }

    private void populateQuestionCheckboxMeasurementType(ViewGroup parentView, final QuestionMeasurementModel model, String initialOptionID,
                                                         QuestionPartModel qpModel, QuestionModel questionModel) {
        //CONTROL FIND
        View containerLinearLayout = LayoutInflater.from(rootActivity).inflate(R.layout.question_checkbox_measurement_item_ph, parentView, false);
        ViewGroup relativeLayout = (ViewGroup) ((ViewGroup) containerLinearLayout).getChildAt(0);
        // ViewGroup ddlLinearLayout = ((ViewGroup) ((ViewGroup) containerLinearLayout).getChildAt(1));

        //TextView questionCheckboxMeasurementTitle_textView = (TextView) ((ViewGroup)((ViewGroup)questionCheckboxMeasurementTypeContainer_linearLayout).getChildAt(0)).getChildAt(0); //ControlParser.parseControl(questionCheckboxMeasurementTypeContainer_linearLayout, R.id.questionCheckboxMeasurementTitle_textView);

        //(a) relative layout
        //measurement text
        TextView questionCheckboxMeasurementTitle_textView = (TextView) relativeLayout.getChildAt(1);
        //camera image
        ImageView camera = (ImageView) relativeLayout.getChildAt(2);
        camera.setVisibility(View.GONE);

        //(b) checkbox
        ImageView questionCheckboxMeasurementInputValue_imageView = (ImageView) (relativeLayout.getChildAt(3));

        ImageView mandatoryQIndicator = (ImageView) (relativeLayout.getChildAt(4));
        mandatoryQIndicator.setVisibility(View.GONE);
        boolean isMeasurementRequired = false;

        for (QuestionMeasurementValidationModel validationModel : model.getQuestionMeasurementValidations()) {
//            CommonFunction.print("IS QUESTION MANDATORY3:" + validationModel.isMeasurementValidationIsRequired());
            isMeasurementRequired = validationModel.isMeasurementValidationIsRequired();
        }

        if (isMeasurementRequired) {
            mandatoryQIndicator.setVisibility(View.VISIBLE);
        }

        //image view
        ImageView itemImage_imageView = (ImageView) relativeLayout.getChildAt(0);

        String imageURL = null;

        for (QuestionPartModel qp : questionModel.getQuestionParts()) {
            if(!model.getMeasurementImage().equalsIgnoreCase("0")){
                itemImage_imageView.setVisibility(View.VISIBLE);
                imageURL = model.getMeasurementImage();
            }else{
                itemImage_imageView.setVisibility(View.GONE);

            }

        }
        new CommonFunction.DownloadImageTask(itemImage_imageView).execute(imageURL);

        String finalImageURL = imageURL;
        itemImage_imageView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Bundle bundle = new Bundle();
                bundle.putString("imageURL", finalImageURL);
                replaceFragment(new ImageFragment(),bundle,FragmentBase.SLIDE_IN_FROM_RIGHT_ANIMATION_KEY);

            }
        });



        //(c) validator
        TextView questionCheckboxMeasurementErrorMessage_textView = (TextView) ((ViewGroup) containerLinearLayout).getChildAt(1);

        //CONTROL SET VALUES
        //show camera >> only when the measurement belongs to the first question part
//        CommonFunction.print("cameraTuple:44C2 : " + questionModel.getPhotoTagsThree().size() + " : " + questionModel.getPhotoTagsTwo().size() + " : " + questionModel.getQuestionPhotoURL() + " : " + questionModel.getPhotoTagsTwo().get(0).getNumberOfPhotos());
        populateCameraButtonOnMeasurement(camera, model, qpModel, questionModel);


        questionCheckboxMeasurementTitle_textView.setText(model.getQuestionMeasurementDescription());

        questionCheckboxMeasurementInputValue_imageView.setImageResource(R.drawable.icon_checkbox_inactive3);

        if (initialOptionID.equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_CHECKED_OPTION_ID_VALUE)) {
            questionCheckboxMeasurementInputValue_imageView.setImageResource(R.drawable.icon_checkbox_active3);
            totalCheckInt++;

        } else if (initialOptionID.equalsIgnoreCase(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_CHECKED_OPTION_ID_VALUE)) {
            questionCheckboxMeasurementInputValue_imageView.setImageResource(R.drawable.icon_checkbox_cross3);
        }

        totalCheck.setText(String.valueOf(totalCheckInt));

        questionCheckboxMeasurementInputValue_imageView.setTag(initialOptionID);

        questionCheckboxMeasurementInputValue_imageView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                requireView().requestFocus();
                CommonFunction.hideSoftKeyboard(requireActivity());

                ImageView imageView = (ImageView) v;

                if (imageView.getTag().equals(QuestionMeasurementModel.QUESTION_MEASUREMENT_CHECKED_OPTION_ID_VALUE)) {
                    imageView.setImageResource(R.drawable.icon_checkbox_cross3);
                    imageView.setTag(QuestionMeasurementModel.QUESTION_MEASUREMENT_NOT_CHECKED_OPTION_ID_VALUE);
                    totalCheckInt--;
                } else {
                    imageView.setImageResource(R.drawable.icon_checkbox_active3);
                    imageView.setTag(QuestionMeasurementModel.QUESTION_MEASUREMENT_CHECKED_OPTION_ID_VALUE);
                    totalCheckInt++;
                }
                if(totalCheckInt<0){
                    totalCheckInt = 0;
                }
                totalCheck.setText(String.valueOf(totalCheckInt));
            }
        });

        questionCheckboxMeasurementErrorMessage_textView.setText("");
        questionCheckboxMeasurementErrorMessage_textView.setVisibility(View.GONE);

        parentView.addView(containerLinearLayout);

        containerLinearLayout.setVisibility(View.VISIBLE);
        if (model.getQuestionMeasurementDefaultAction().equalsIgnoreCase(QuestionModel.QUESTION_ACTION_DISAPPEAR_KEY)) {
            containerLinearLayout.setVisibility(View.GONE);
        }

        HashMap< String, Object > layoutMap = new HashMap<>();
        layoutMap.put(FORM_LAYOUT_UI_CONTAINER_OBJECT_KEY, containerLinearLayout);
        layoutMap.put(FORM_LAYOUT_UI_INPUT_OBJECT_KEY, questionCheckboxMeasurementInputValue_imageView);
        layoutMap.put(FORM_LAYOUT_UI_ERROR_MESSAGE_OBJECT_KEY, questionCheckboxMeasurementErrorMessage_textView);

        HashMap< String, Object > parentMap = (HashMap< String, Object >) CommonFunction.getParentMap(formLayout_hashMap, parentView);
        parentMap.put(model.getQuestionMeasurementID(), layoutMap);

    }

    private void populateQuestionMultiSelectMeasurementType(ViewGroup parentView, final QuestionMeasurementModel model, String initialTextResult,
                                                            final QuestionModel questionModel, QuestionPartModel qpModel) {


        View containerLinearLayout = LayoutInflater.from(rootActivity).inflate(R.layout.question_multiselect_measurement_item_ph, parentView, false);
        ViewGroup relativeLayout = ((ViewGroup) ((ViewGroup) containerLinearLayout).getChildAt(0));

        ViewGroup ddlLinearLayoutMand = ((ViewGroup) ((ViewGroup) containerLinearLayout).getChildAt(1));
        ViewGroup ddlLinearLayout = (((ViewGroup) ((ViewGroup) ((ViewGroup) containerLinearLayout).getChildAt(1)).getChildAt(0)));

        //(a)title
        TextView questionMultiSelectMeasurementTitle_textView = (TextView) (relativeLayout.getChildAt(0)); //ControlParser.parseControl(questionMultiSelectMeasurementTypeContainer_linearLayout, R.id.questionMultiSelectMeasurementTitle_textView);
        //camera image
        ImageView camera = (ImageView) relativeLayout.getChildAt(1);
        camera.setVisibility(View.GONE);


        //(b) user control
//        RelativeLayout questionMultiSelectMeasurementInputContainer_relativeView = (RelativeLayout) ((ViewGroup)containerLinearLayout).getChildAt(1); //ControlParser.parseControl(questionMultiSelectMeasurementTypeContainer_linearLayout, R.id.questionMultiSelectMeasurementInputContainer_relativeView);
        TextView questionMultiSelectMeasurementInputValue_textView = (TextView) ddlLinearLayout.getChildAt(0); //ControlParser.parseControl(questionMultiSelectMeasurementTypeContainer_linearLayout, R.id.questionMultiSelectMeasurementInputValue_textView);

        ImageView mandatoryQIndicator = (ImageView) (ddlLinearLayoutMand.getChildAt(1));
        mandatoryQIndicator.setVisibility(View.GONE);
        boolean isMeasurementRequired = false;

        for (QuestionMeasurementValidationModel validationModel : model.getQuestionMeasurementValidations()) {

            isMeasurementRequired = validationModel.isMeasurementValidationIsRequired();
        }

        if (isMeasurementRequired) {
            mandatoryQIndicator.setVisibility(View.VISIBLE);
        }

        //(c) validation
        TextView questionMultiSelectMeasurementErrorMessage_textView = (TextView) ((ViewGroup) containerLinearLayout).getChildAt(2); //ControlParser.parseControl(questionMultiSelectMeasurementTypeContainer_linearLayout, R.id.questionMultiSelectMeasurementErrorMessage_textView);

        //CONTROL SET VALUE
        camera.setVisibility(View.GONE);
        //show camera >> only when the measurement belongs to the first question part
        populateCameraButtonOnMeasurement(camera, model, qpModel, questionModel);

        questionMultiSelectMeasurementTitle_textView.setText(model.getQuestionMeasurementDescription());

        if (!CommonFunction.isEmptyStringField(initialTextResult)) {
            questionMultiSelectMeasurementInputValue_textView.setText(initialTextResult);
            questionMultiSelectMeasurementInputValue_textView.setTextColor(Color.BLACK);
        } else {
            questionMultiSelectMeasurementInputValue_textView.setText(getString(R.string.MultiSelectionDropDownHintText));
            questionMultiSelectMeasurementInputValue_textView.setTextColor(ContextCompat.getColor(requireContext(), R.color.inActiveTextColor));
        }

        containerLinearLayout.setTag(model);
        containerLinearLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                requireView().requestFocus();
                CommonFunction.hideSoftKeyboard(requireActivity());

                final QuestionMeasurementModel model = (QuestionMeasurementModel) v.getTag();

                selectedQuestionMeasurementMultiSelect_textView = (TextView) ddlLinearLayout.getChildAt(0); //ControlParser.parseControl(v, R.id.questionMultiSelectMeasurementInputValue_textView);

                String currentValue = selectedQuestionMeasurementMultiSelect_textView.getText().toString();
                currentValue = currentValue.replace(getString(R.string.MultiSelectionDropDownHintText), "");

                selectedQuestionMeasurementMultiItems.clear();
                selectedQuestionMeasurementMultiItems.addAll(Arrays.asList(currentValue.split("\\|")));

                int multiSelectSize = model.getQuestionMeasurementOptions().size();
                String[] multiSelectValues = new String[multiSelectSize];
                boolean[] selectedStates = new boolean[multiSelectSize];

                for (int i = 0; i < multiSelectSize; i++) {
                    String multiSelectValue = Objects.requireNonNull(model.getQuestionMeasurementOptions().get(i)).getMeasurementOptionDescription();
                    multiSelectValues[i] = multiSelectValue;
                    selectedStates[i] = selectedQuestionMeasurementMultiItems.contains(multiSelectValue);
                }

                AlertDialog.Builder builder = new AlertDialog.Builder(rootActivity);

                builder.setTitle(getString(R.string.MultiSelectionDropDownHintText).replace("...", ""))
                        .setMultiChoiceItems(multiSelectValues, selectedStates, new DialogInterface.OnMultiChoiceClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which, boolean isChecked) {
                                ListView listView = ((AlertDialog) dialog).getListView();
                                String selectedString = (String) listView.getAdapter().getItem(which);

                                if (isChecked) {
                                    selectedQuestionMeasurementMultiItems.add(selectedString);
                                } else selectedQuestionMeasurementMultiItems.remove(selectedString);
                            }
                        })
                        .setPositiveButton("Done", new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int id) {
                                // Done selected


                                if (selectedQuestionMeasurementMultiItems.size() > 0) {
                                    //any mo ticked
                                    selectedQuestionMeasurementMultiSelect_textView.setText(CommonFunction.combineArrayListIntoString(selectedQuestionMeasurementMultiItems, "|"));
                                    selectedQuestionMeasurementMultiSelect_textView.setTextColor(Color.BLACK);

                                } else {
                                    //no mo ticked
                                    selectedQuestionMeasurementMultiSelect_textView.setText(getString(R.string.MultiSelectionDropDownHintText));
                                    selectedQuestionMeasurementMultiSelect_textView.setTextColor(ContextCompat.getColor(requireContext(), R.color.inActiveTextColor));
                                }

                                selectedQuestionMeasurementMultiSelect_textView = null;

                            }
                        })
                        .setNegativeButton("Cancel", new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int id) {
                                selectedQuestionMeasurementMultiItems.clear();
                                selectedQuestionMeasurementMultiSelect_textView = null;
//                                selectedQuestionMeasurementMultiSelect_textView.setText(getString(R.string.MultiSelectionDropDownHintText));
                            }
                        })
                        .setOnDismissListener(new DialogInterface.OnDismissListener() {
                            @Override
                            public void onDismiss(DialogInterface dialog) {
                                selectedQuestionMeasurementMultiItems.clear();
                                selectedQuestionMeasurementMultiSelect_textView = null;

                                CommonFunction.getInstance(getContext()).dismissAlertDialog((AlertDialog) dialog);
                            }
                        });


                AlertDialog alertDialog = builder.create();
                alertDialog.show();

                CommonFunction.getInstance(getContext()).recordShownAlertDialog(alertDialog);
            }
        });

        questionMultiSelectMeasurementErrorMessage_textView.setText("");
        questionMultiSelectMeasurementErrorMessage_textView.setVisibility(View.GONE);

        parentView.addView(containerLinearLayout);

        containerLinearLayout.setVisibility(View.VISIBLE);
        if (model.getQuestionMeasurementDefaultAction().equalsIgnoreCase(QuestionModel.QUESTION_ACTION_DISAPPEAR_KEY)) {
            containerLinearLayout.setVisibility(View.GONE);
        }

        HashMap< String, Object > layoutMap = new HashMap<>();
        layoutMap.put(FORM_LAYOUT_UI_CONTAINER_OBJECT_KEY, containerLinearLayout);
        layoutMap.put(FORM_LAYOUT_UI_INPUT_OBJECT_KEY, questionMultiSelectMeasurementInputValue_textView);
        layoutMap.put(FORM_LAYOUT_UI_ERROR_MESSAGE_OBJECT_KEY, questionMultiSelectMeasurementErrorMessage_textView);

        HashMap< String, Object > parentMap = (HashMap< String, Object >) CommonFunction.getParentMap(formLayout_hashMap, parentView);
        parentMap.put(model.getQuestionMeasurementID(), layoutMap);

    }


    /// m condition fired >> show measurement which was invisible and show/hide camera icon as well.

    private void showTargetMeasurementHidden(View m_targetView) {
        m_targetView.setVisibility(View.VISIBLE);
        if(resetSpinner){
            resetAllSpinnersInView(m_targetView);
        }
    }



    /**
     * Resets all Spinner views within the given view hierarchy to their default selection.
     * Traverses recursively through nested ViewGroups to ensure all spinners are handled.
     *
     * @param view the root view to search for spinners.
     *
     * Author: Nirvik
     */
    private void resetAllSpinnersInView(View view) {
        if (view instanceof Spinner) {
            ((Spinner) view).setSelection(0);
        } else if (view instanceof ViewGroup viewGroup) {
            for (int i = 0; i < viewGroup.getChildCount(); i++) {
                resetAllSpinnersInView(viewGroup.getChildAt(i));
            }
        }
    }



    // m condition fired >> hide measurement that was shown
    private void hideTargetMeasurementShown(View m_targetView) {
        m_targetView.setVisibility(View.GONE);

    }


    /// When question/subheader/measurment controls are populating, populate camera in each measurement.
    /// Decide whether to show camera icon or not. Also decide whether yellow/green camera button.
    /// There are 3 funcitons related to camera
    /// (1) populateCameraButtonOnMeasurement() - initialize
    /// (2) showCameraButton() - show and configure (show icon or user photo)
    /// (3) hideCameraButton() - hide it
    private void populateCameraButtonOnMeasurement(ImageView camera,
                                                   final QuestionMeasurementModel mModel,
                                                   QuestionPartModel qpModel,
                                                   QuestionModel qModel
    ) {
        final String mid = mModel.getQuestionMeasurementID();
        final String qid = qModel.getQuestionID();
        final String qpid = qpModel.getQuestionPartID();
        final String qmpid = qpModel.getQuestionPartMultiID();


        camera.setImageDrawable(null);
        camera.setVisibility(View.GONE);

        if (qModel.hasPhotoTagThree(qpid, mid)) {
            //mo, 16/5/18, test showing >> show always ----------------
            //camera.setVisibility(View.VISIBLE);
            //camera.setImageResource(R.drawable.icon_camera_yellow);
            //---------------------------------------------------------
            CameraTuple cameraTuple = DatabaseManager.getInstance(getContext()).getCameraInfoForHasPhotoTagThree(ActivityBase.mRealm, taskID, formID, qid, qpid, mid, qmpid);
            System.out.println("cameraTuple:" + cameraTuple.toString());

            if (cameraTuple.isShow()) {
                //replace camera with user photo

                if (cameraTuple.getImage().getPhotoCaption().equals(getString(R.string.YellowCamera))) {
                    camera.setImageResource(R.drawable.icon_camera_yellow);
                    camera.setTag(R.drawable.icon_camera_yellow);
                } else if (cameraTuple.getImage().getPhotoCaption().equals(getString(R.string.GreenCamera))) {
                    camera.setImageResource(R.drawable.icon_camera_green);
                } else {
                    //we assume as representative photo
                    showPhotoRepresentative(camera, cameraTuple.getImage());
                }

                //show camera only when measurement has measurement_phototypes
                camera.setVisibility(View.VISIBLE);

                //mpt camera button clicked
                camera.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        replaceFragment2MPTList(qid, qpid, mid, qmpid);

                    }
                });
            }
        }

    }

    //    //mo, 17/5/18, parameter type changed from PhotoModel to GalleryPhoto to cope with other functions.
    private void showPhotoRepresentative(ImageView imgPhoto, GalleryPhoto photo) {
        //without below, old bitmap (not rotated etc, still shows)
        //CommonFunction.recycleImageViewBitmap(imgPhoto);
        imgPhoto.setImageDrawable(null);

        CommonFunction.print("cameraTuple: REP " + photo.getPhotoLocalPath());

        if (photo.isCannotUploadMandatory()) {
            imgPhoto.setImageResource(R.drawable.unable_to_load_photo_v3);
        } else {
            if (photo.getPhotoLocalPath() != null && (!photo.getPhotoLocalPath().equalsIgnoreCase(Constant.INVALID_RESOURCE_URL_MARKER_STRING))) {

                Bitmap localBitmap = CommonFunction.decodeSampledBitmapFromUri(getContext(), Uri.parse(FileProvider.contentUri
                        + photo.getPhotoLocalPath()));

                localBitmap = CommonFunction.getRoundedCornerBitmap(localBitmap, 50);
                imgPhoto.setImageBitmap(localBitmap);

                imgPhoto.setScaleType(ImageView.ScaleType.CENTER_CROP);

                RoundedBitmapDrawable img = RoundedBitmapDrawableFactory.create(getResources(), localBitmap);
                img.setCornerRadius(50);

                imgPhoto.setImageDrawable(img);
            }else{
                imgPhoto.setImageResource(R.drawable.unable_to_load_photo_v3);
            }
        }
    }

    private void replaceFragment2MPTList(String qid, String qpid, String mid, String qmpid) {

        Bundle fragmentBundle = new Bundle();
        fragmentBundle.putString(MPTListFragment.MPT_LIST_TITLE_KEY, "Photo Upload");
        fragmentBundle.putString(PhotoTagModel.TASK_ID_KEY, taskID);
        fragmentBundle.putString(PhotoTagModel.FID_KEY, formID);
        fragmentBundle.putString(PhotoTagModel.QID_KEY, qid);
        fragmentBundle.putString(PhotoTagModel.QPID_KEY, qpid);
        fragmentBundle.putString(PhotoTagModel.MID_KEY, mid);
        fragmentBundle.putString(PhotoTagModel.CBID_KEY, "3");
        fragmentBundle.putString(PhotoTagModel.QMPID_KEY, qmpid);

        //nirvik, I am saving the form before user is taken to the photo page
        saveFunction(false);

        replaceFragment(new MPTListFragment(), fragmentBundle, FragmentBase.SLIDE_IN_FROM_RIGHT_ANIMATION_KEY);
    }


    //no1. onCreateView() >> ok
    //no2. addNewQuestionPart() >> no good
    //no3. deleteQuestionPartFromDb() >> ok (all previous answers should have been saved in no2).
    private void loadQuestionPartsMeasurements() {

        formLayout_hashMap.clear();
        taskFormQuestionContainer_linearLayout.removeAllViews();

        if ((questionModel.getQuestionParts() != null) &&
                (questionModel.getQuestionParts().size() > 0)) {

            for (QuestionPartModel questionPartModels : questionModel.getQuestionParts()) {

                if ((questionPartModels.getQuestionPartID() != null) && questionPartModels.getQuestionPartMultiID().equalsIgnoreCase(questionMultiPartID)) {

                    if (!CommonFunction.isEmptyStringField(questionPartModels.getQuestionPartDescription())) {
                        //(1) Question Header , which shows on top of the page below top nav bar

                        ViewGroup questionHeader = populateQuestionHeader(taskFormQuestionContainer_linearLayout, questionModel);

                        // Not comment

                        //existing code
                        if (!CommonFunction.isEmptyStringField(questionPartModels.getQuestionPartDescription())) {

                            final ViewGroup questionSubHeader = populateQuestionSubHeader(questionHeader, questionPartModels);
                            final ViewGroup question_linerLayout_Container = questionSubHeader.findViewById(R.id.question_linerLayout_Container);

                            // (3) Measurements (with photo(?) and answer)
                            if (question_linerLayout_Container != null) {
                                question_linerLayout_Container.setVisibility(View.VISIBLE);
                                populateQuestionMeasuremet(question_linerLayout_Container, questionModel, questionPartModels);
                            }
                        }
                    }
                }
            }
        }

    }


    private static void addQuestionState(ArrayList< String > aListNumbers, String str) {

        /*
         * Check if the element already exists in the ArrayList.
         *
         * If it exists, do not add it. Otherwise add it to
         * the ArrayList
         */

        //if element does not exists in the ArrayList, add it
        if (!aListNumbers.contains(str)) {
            aListNumbers.add(str);
        }

        //this means element was already there in the ArrayList, return false
    }

    private static void removeQuestionState(ArrayList< String > aListNumbers, String str) {

        /*
         * Check if the element already exists in the ArrayList.
         *
         * If it exists, do not add it. Otherwise add it to
         * the ArrayList
         */

        //if element does not exists in the ArrayList, add it
        aListNumbers.remove(str);

        //this means element was already there in the ArrayList, return false
    }

    private static class QuestionSaveInfo {
        String qid;
        boolean has_m_mandatory;
        boolean is_q_answered;

        //constructor
        private QuestionSaveInfo() {
            this.qid = "";
            this.has_m_mandatory = false;
            this.is_q_answered = false;
        }
    }
}