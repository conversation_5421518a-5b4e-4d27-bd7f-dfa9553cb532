package com.au.storetrack.Fragments.Task.PreviousTask;

import android.app.AlertDialog;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.AsyncTask;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.au.storetrack.Activities.ActivityBase;
import com.au.storetrack.Fragments.Base.FragmentBase;
import com.au.storetrack.Model.DataModel.PhotoModel;
import com.au.storetrack.Model.DataModel.PreviousTaskFormModel;
import com.au.storetrack.Model.DataModel.QuestionAnswerModel;
import com.au.storetrack.Model.DataModel.QuestionMeasurementModel;
import com.au.storetrack.Model.DataModel.QuestionModel;
import com.au.storetrack.Model.DataModel.QuestionPartModel;
import com.au.storetrack.Model.DataModel.TaskDetailModel;
import com.au.storetrack.Model.EventModel.APIEvent;
import com.au.storetrack.R;
import com.au.storetrack.Utilities.CommonFunction;
import com.au.storetrack.Utilities.ControlParser;
import com.au.storetrack.Utilities.Database.DatabaseManager;
import com.au.storetrack.Utilities.Network.API;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * Created by Augustin on 16/5/2022.
 */

public class PreviousTaskFormDetailFragment extends FragmentBase {


    public static final String FORM_QUESTION_PART_DETAIL_TITLE_KEY = "FORM_QUESTION_PART_DETAIL_TITLE_KEY";

    int counter = 0;

    private String taskID, formID;
    List<PhotoModel> photoModels;
    private PreviousTaskFormModel previousTaskFormModel;
    private LinearLayout questions_container_linearLayout, question_header_container_linearLayout, questions_results_container_linearLayout,qp_linear;

    //for debug
    private String TAG = "FormQuestionSignatureFragment";
    List<String> photoList = new ArrayList<>();
    List<String> questionPartPhotoList = new ArrayList<>();


    HashMap<String, Integer> photoCounterMap = new HashMap<>();
    HashMap<String, Integer> measurementPhotoCounterMap = new HashMap<>();

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        EventBus.getDefault().register(this);
        CommonFunction.hideSoftKeyboard(requireActivity());

        Bundle receiveBundle = getArguments();

        View v = inflater.inflate(R.layout.previous_task_form_detail, container, false);
        questions_container_linearLayout = ControlParser.parseControl(v, R.id.questions_container_linearLayout);

        //get starting taskid anf formid
        //get form model using starting IDs
        if (Objects.requireNonNull(receiveBundle).containsKey(TaskDetailModel.TASK_ID_KEY) &&
                (receiveBundle.containsKey(PreviousTaskFormModel.FORM_ID_KEY))) {

            taskID = String.valueOf(receiveBundle.getInt(TaskDetailModel.TASK_ID_KEY));
            formID = receiveBundle.getString(PreviousTaskFormModel.FORM_ID_KEY);
            previousTaskFormModel = DatabaseManager.getInstance(getContext()).getFormModelFromTaskIDWithFormIDPreviousTask(ActivityBase.mRealm, taskID, formID, true);

            photoModels = DatabaseManager.getInstance(getContext()).getPreviousTaskPhotos(ActivityBase.mRealm, taskID,formID,true);

        }


        //error if task detail request == null
        if ((taskID == null) || (formID == null) || (previousTaskFormModel == null)) {
            AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
            builder.setTitle("Fail to initialise");
            builder.setMessage("Please try again later.");
            builder.setCancelable(false);
            builder.setPositiveButton(R.string.OK, (dialog, id) -> popFragment());

            builder.setOnDismissListener(dialog -> CommonFunction.getInstance(getContext()).dismissAlertDialog((AlertDialog) dialog));
            AlertDialog alertDialog = builder.create();
            alertDialog.show();

            CommonFunction.getInstance(getContext()).recordShownAlertDialog(alertDialog);
        } else {
            prepareInterfaces(v);
        }

        return v;
    }


    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onDestroyView() {
        try {

            CommonFunction.hideSoftKeyboard(requireActivity());
            EventBus.getDefault().unregister(this);
        } catch (Exception e) {
        }

        super.onDestroyView();
    }

    @Subscribe
    public void onAPIEventReceived(APIEvent event) {
        switch (event.getApiRequestTag()) {
            case API.API_DOWNLOAD_FILE_REQUEST_TAG:
                if (event.getApiResponseType().equalsIgnoreCase(API.API_SUCCESS_RESPONSE_TYPE)) {
//                   refreshDisplayedInformation();
                }

                break;

            default:
                break;
        }
    }


    private void prepareInterfaces(View v) {
        if(previousTaskFormModel != null)
        {
            loadformQuestionMeasurement(previousTaskFormModel);
        }
    }

    @Override
    public void setNavigationBarContent() {

        navigationBar.clearAllControls();

        ImageView leftControl = new ImageView(rootActivity);
        leftControl.setImageResource(R.drawable.btn_back);
        leftControl.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                popFragment();
            }
        });

        navigationBar.addLeftNavigationBarControl(leftControl);

        navigationBar.setNavigationBarCenterTitleTextMenuPage("Previous Task Form");

    }



    private void loadformQuestionMeasurement(PreviousTaskFormModel formModel) {

        questions_container_linearLayout.removeAllViews();

        for (QuestionModel questionModel : formModel.getFormQuestions()) {

            if(questionModel !=null)
            {
                populateQuestions(questions_container_linearLayout, questionModel);
            }
        }
    }

    private ViewGroup populateQuestions(ViewGroup parentView, QuestionModel questionModel) {


        View questionHeaderContainer_linearLayout = LayoutInflater.from(rootActivity).inflate(R.layout.question_header, parentView, false);
        questionHeaderContainer_linearLayout.setVisibility(View.GONE);
        question_header_container_linearLayout = ControlParser.parseControl(questionHeaderContainer_linearLayout, R.id.questionDividerContainer_linearLayout);

        TextView questionTextView = ControlParser.parseControl(questionHeaderContainer_linearLayout, R.id.question_text_textview);
        ImageView questionImageView = ControlParser.parseControl(questionHeaderContainer_linearLayout, R.id.question_image);
        TextView questionPhotoCountersView = ControlParser.parseControl(questionHeaderContainer_linearLayout, R.id.photo_counter);




    for(QuestionPartModel questionPartModel : questionModel.getQuestionParts()){
        for (QuestionAnswerModel qa : previousTaskFormModel.getFormQuestionAnswers()) {
            if (qa != null) {
                if (qa.getQuestionAnswerQuestionPartID().equalsIgnoreCase(questionPartModel.getQuestionPartMultiID())) {


                    for (QuestionMeasurementModel qm : questionModel.getQuestionMeasurements()) {
                        if (qm != null) {
                            if (qm.getQuestionMeasurementID().equalsIgnoreCase(qa.getQuestionAnswerMeasurementID())) {
                                if (!qa.getQuestionAnswerMeasurementTextResult().equalsIgnoreCase("-")) {
                                    questionHeaderContainer_linearLayout.setVisibility(View.VISIBLE);
                                    questionTextView.setText(questionModel.getQuestionDescription());

                                }
                            }
                        }
                    }

                }
            }
        }

    }
        //nirvik, todo: BEGIN: need to optimize below
        //nirvik, FINAL: no need to optimize below
        for(int i = 0; i < photoModels.size(); i++){
            PhotoModel photoModel = photoModels.get(i);
            String questionID1 = photoModel.getQuestionID();
            if(questionID1.equals(questionModel.getQuestionID())){
                if(photoModel.getMeasurementID().equals("0")){
                    if(photoCounterMap.containsKey(questionID1)){
                        photoCounterMap.put(questionID1, photoCounterMap.get(questionID1)+ 1);
                    }else{
                        photoCounterMap.put(questionID1, 1);
                    }
                }
            }
        }
        if(photoCounterMap.containsKey(questionModel.getQuestionID())){
            questionPhotoCountersView.setVisibility(View.VISIBLE);
            questionImageView.setVisibility(View.VISIBLE);
            questionPhotoCountersView.setText(String.valueOf(photoCounterMap.get(questionModel.getQuestionID())));
        }else{
            questionPhotoCountersView.setVisibility(View.GONE);
            questionImageView.setVisibility(View.GONE);
        }
        //todo:END


        questionImageView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                measurementPhotoCounterMap.clear();
                photoCounterMap.clear();

                Bundle bundle = new Bundle();
                bundle.putString("taskID",taskID);
                bundle.putString("formID",formID);
                bundle.putString("questionID",questionModel.getQuestionID());
                replaceFragment(new PreviousTaskPhotoFragment(),bundle,FragmentBase.SLIDE_IN_FROM_RIGHT_ANIMATION_KEY);
            }
        });

      //  question_header_container_linearLayout.removeAllViews();

        for (QuestionPartModel questionPartModel : questionModel.getQuestionParts()) {

            populateQuestionParts(question_header_container_linearLayout, questionModel, questionPartModel);

            }


        parentView.addView(questionHeaderContainer_linearLayout);
        return (ViewGroup) questionHeaderContainer_linearLayout;
    }

//nirvik, optimization for image loading [Redundant] but I am still keeping this
    private class DownloadImageTask extends AsyncTask<String, Void, Bitmap> {
        ImageView bmImage;

        public DownloadImageTask(ImageView bmImage) {
            this.bmImage = bmImage;
        }

        protected Bitmap doInBackground(String... urls) {
            String urldisplay = urls[0];
            Bitmap mIcon11 = null;
            try {
                InputStream in = new java.net.URL(urldisplay).openStream();
                mIcon11 = BitmapFactory.decodeStream(in);
            } catch (Exception e) {
                Log.e("Error", e.getMessage());
                e.printStackTrace();
            }
            return mIcon11;
        }

        protected void onPostExecute(Bitmap result) {
            bmImage.setImageBitmap(result);
        }
    }

    private ViewGroup populateQuestionParts(ViewGroup parentView, QuestionModel questionModel, QuestionPartModel questionPartModel) {


        View questionPartHeaderContainer_linearLayout = LayoutInflater.from(rootActivity).inflate(R.layout.question_part_header, parentView, false);
        questions_results_container_linearLayout = ControlParser.parseControl(questionPartHeaderContainer_linearLayout, R.id.questionResultsContainer_linearLayout);
        qp_linear = ControlParser.parseControl(questionPartHeaderContainer_linearLayout, R.id.qp_linear);
        TextView questionPartTextView = ControlParser.parseControl(questionPartHeaderContainer_linearLayout, R.id.question_part_text_textview);
        qp_linear.setVisibility(View.GONE);

       // ImageButton dropDown = ControlParser.parseControl(questionPartHeaderContainer_linearLayout, R.id.dropDownIcon_ib);

//        dropDown.setOnClickListener(v -> {
//            if(questions_results_container_linearLayout.getVisibility() == View.VISIBLE){
//                questions_results_container_linearLayout.setVisibility(View.GONE);
//                dropDown.setImageDrawable(getResources().getDrawable(R.drawable.icon_down));
//            }else if(questions_results_container_linearLayout.getVisibility() == View.GONE){
//                questions_results_container_linearLayout.setVisibility(View.VISIBLE);
//                dropDown.setImageDrawable(getResources().getDrawable(R.drawable.icon_up));
//
//            }
//        });


            for (QuestionAnswerModel qa : previousTaskFormModel.getFormQuestionAnswers()) {
                if (qa != null) {
                    if (qa.getQuestionAnswerQuestionPartID().equalsIgnoreCase(questionPartModel.getQuestionPartMultiID())) {


                        for (QuestionMeasurementModel qm : questionModel.getQuestionMeasurements()) {
                            if (qm != null) {
                                if (qm.getQuestionMeasurementID().equalsIgnoreCase(qa.getQuestionAnswerMeasurementID())) {
                                    if (!qa.getQuestionAnswerMeasurementTextResult().equalsIgnoreCase("-")) {
                                        qp_linear.setVisibility(View.VISIBLE);
                                        questionPartTextView.setText(questionPartModel.getQuestionPartDescription());

                                    }
                                }
                            }
                        }

                    }
                }
            }






       // System.out.println("Question Part :"+ questionPartModel.getQuestionPartDescription());

       // questions_results_container_linearLayout.removeAllViews();

  //  System.out.println("This is the Question :" + questionModel.getQuestionMeasurements().get(0));
       // parentView.removeAllViews();
        populateMeasurements(questionModel, questionPartModel);
        parentView.addView(questionPartHeaderContainer_linearLayout);
        return (ViewGroup) questionPartHeaderContainer_linearLayout;

    }

    private void populateMeasurements(QuestionModel questionModel, QuestionPartModel questionPartModel) {
        for (QuestionAnswerModel qa : previousTaskFormModel.getFormQuestionAnswers()) {

            if (qa != null) {
                if (qa.getQuestionAnswerQuestionPartID().equalsIgnoreCase(questionPartModel.getQuestionPartMultiID())) {
                    for (QuestionMeasurementModel qm : questionModel.getQuestionMeasurements()) {
                        if (qm != null) {
                            if (qm.getQuestionMeasurementID().equalsIgnoreCase(qa.getQuestionAnswerMeasurementID())) {
                                if (!qa.getQuestionAnswerMeasurementTextResult().equalsIgnoreCase("-")) {
                                    populateQuestionMeasurements(questions_results_container_linearLayout, qm, qa);
                                }

                            }
                        }
                    }
                }
            }
        }
    }

    private ViewGroup populateQuestionMeasurements(ViewGroup parentView, QuestionMeasurementModel qm, QuestionAnswerModel questionPartAnswers) {
        View questionResultsContainer_linearLayout = LayoutInflater.from(rootActivity).inflate(R.layout.signature_results, parentView, false);

        TextView measurementTextView = ControlParser.parseControl(questionResultsContainer_linearLayout, R.id.measurement_textview);
        TextView responseTextView = ControlParser.parseControl(questionResultsContainer_linearLayout, R.id.response_textview);
        ImageView questionPartImageView = ControlParser.parseControl(questionResultsContainer_linearLayout, R.id.question_part_image);
        TextView questionPartPhotoCountersView= ControlParser.parseControl(questionResultsContainer_linearLayout, R.id.photo_counter);
        LinearLayout qa_container = ControlParser.parseControl(questionResultsContainer_linearLayout,R.id.qa_container);
        qa_container.setVisibility(View.GONE);
        if(!questionPartAnswers.getQuestionAnswerMeasurementTextResult().equalsIgnoreCase("")){
            qa_container.setVisibility(View.VISIBLE);
            measurementTextView.setText(qm.getQuestionMeasurementDescription());
            responseTextView.setText(questionPartAnswers.getQuestionAnswerMeasurementTextResult());
        }


        System.out.println("Question and Answer:"+qm.getQuestionMeasurementDescription()+":"+questionPartAnswers.getQuestionAnswerMeasurementTextResult());
        //nirvik, todo: BEGIN: need to optimize below
        for(int i = 0; i < photoModels.size(); i++){
            PhotoModel photoModel = photoModels.get(i);
            String questionID1 = photoModel.getMeasurementID();
            String questionID2 = qm.getQuestionMeasurementID();

            if(!questionID1.equals("0")){
                if(questionID1.equals(questionID2)){
                    questionPartPhotoList.add(photoModel.getPhotoURL());
                    if(questionPartPhotoList.size() != 0){

                            if(measurementPhotoCounterMap.containsKey(questionID1)){
                                measurementPhotoCounterMap.put(questionID1, measurementPhotoCounterMap.get(questionID1)+ 1);
                            }else{
                                measurementPhotoCounterMap.put(questionID1, 1);
                            }

                    }
                }
            }

            if(measurementPhotoCounterMap.containsKey(qm.getQuestionMeasurementID())){
                questionPartPhotoCountersView.setVisibility(View.VISIBLE);
                questionPartImageView.setVisibility(View.VISIBLE);
                questionPartPhotoCountersView.setText(String.valueOf(measurementPhotoCounterMap.get(qm.getQuestionMeasurementID())));
            }else{
                questionPartPhotoCountersView.setVisibility(View.GONE);
                questionPartImageView.setVisibility(View.GONE);
            }
            questionPartImageView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    measurementPhotoCounterMap.clear();
                    photoCounterMap.clear();
                    Bundle bundle = new Bundle();
                    bundle.putString("taskID",taskID);
                    bundle.putString("formID",formID);
                    bundle.putString("measurementID",qm.getQuestionMeasurementID());
                    replaceFragment(new PreviousTaskPhotoFragment(),bundle,FragmentBase.SLIDE_IN_FROM_RIGHT_ANIMATION_KEY);
                }
            });
        }
        //todo:END
        parentView.addView(questionResultsContainer_linearLayout);

        return (ViewGroup) questionResultsContainer_linearLayout;
    }

}
