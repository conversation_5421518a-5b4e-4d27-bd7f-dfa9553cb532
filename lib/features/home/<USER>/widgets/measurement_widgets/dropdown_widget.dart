import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/photo_upload_widget.dart'
    show PhotoUploadWidget;

class DropdownWidget extends StatelessWidget {
  final Measurement measurement;
  final String? value;
  final Function(String?) onChanged;
  final bool showCameraIcon;
  final bool isCameraMandatory;
  final VoidCallback? onCameraTap;
  final bool isRequired;
  final String? errorText;
  final List<String> selectedImages;
  final String? photoErrorText;
  final PhotoTagsT? photoTag;

  const DropdownWidget({
    super.key,
    required this.measurement,
    required this.value,
    required this.onChanged,
    this.showCameraIcon = false,
    this.isCameraMandatory = false,
    this.onCameraTap,
    this.isRequired = false,
    this.errorText,
    this.selectedImages = const [],
    this.photoErrorText,
    this.photoTag,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final options = measurement.measurementOptions ?? [];

    // Debug logging for dropdown error investigation
    print('=== DropdownWidget Debug ===');
    print('Measurement ID: ${measurement.measurementId}');
    print('Measurement Type ID: ${measurement.measurementTypeId}');
    print('Measurement Description: ${measurement.measurementDescription}');
    print('Current value: "$value" (type: ${value.runtimeType})');
    print(
        'Measurement defaults result: "${measurement.measurementDefaultsResult}"');
    print('Measurement type name: "${measurement.measurementTypeName}"');
    print('Measurement default action: "${measurement.defaultAction}"');
    print('Total options count: ${options.length}');

    if (options.isEmpty) {
      print('WARNING: No dropdown options available!');
    } else {
      print('Options details:');
      for (int i = 0; i < options.length; i++) {
        final option = options[i];
        print(
            '  [$i] ID: ${option.measurementOptionId}, Description: "${option.measurementOptionDescription}"');
        print(
            '      Budget Offset: ${option.budgetOffset}, Type: ${option.budgetOffsetType}');
        print(
            '      Is Answer: ${option.isAnswer}, Modified: ${option.modifiedTimeStampMeasurementoption}');
      }

      // Check for duplicate descriptions
      final descriptions =
          options.map((o) => o.measurementOptionDescription).toList();
      final uniqueDescriptions = descriptions.toSet();
      if (descriptions.length != uniqueDescriptions.length) {
        print('ERROR: Found duplicate descriptions!');
        final duplicates = <String>[];
        for (final desc in uniqueDescriptions) {
          final count = descriptions.where((d) => d == desc).length;
          if (count > 1) {
            duplicates.add('$desc (${count}x)');
          }
        }
        print('Duplicates: ${duplicates.join(', ')}');
      }

      // Check if current value matches any option
      final matchingOptions = options
          .where((o) => o.measurementOptionDescription == value)
          .toList();
      print('Current value matches ${matchingOptions.length} options');
      if (matchingOptions.isEmpty && value != null && value!.isNotEmpty) {
        print(
            'WARNING: Current value "$value" does not match any dropdown option!');
      }
    }
    print('=== End Debug ===');

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title with required indicator
          Row(
            children: [
              Expanded(
                child: Text(
                  measurement.measurementDescription ?? 'Select Option',
                  style: textTheme.montserratTitleExtraSmall,
                ),
              ),
              if (isRequired)
                Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.priority_high,
                    color: AppColors.loginRed,
                    size: 16,
                  ),
                ),
            ],
          ),
          const Gap(16),
          DropdownButtonFormField<String>(
            value: value,
            borderRadius: BorderRadius.circular(10.0),
            dropdownColor: Colors.white,
            decoration: InputDecoration(
              hintText: 'Select...',
              hintStyle: textTheme.montserratTitleExtraSmall.copyWith(
                color: AppColors.blackTint1,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.0),
                borderSide: BorderSide(
                  color: errorText != null ? Colors.red : AppColors.blackTint2,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.0),
                borderSide: BorderSide(
                  color: errorText != null ? Colors.red : AppColors.blackTint2,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.0),
                borderSide: BorderSide(
                  color: errorText != null ? Colors.red : AppColors.primaryBlue,
                  width: 2,
                ),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12.0,
                vertical: 8.0,
              ),
              errorText: errorText,
              errorStyle: textTheme.montserratTableSmall.copyWith(
                color: Colors.red,
              ),
            ),
            isExpanded: true,
            items: options.map((option) {
              return DropdownMenuItem<String>(
                value: option.measurementOptionDescription,
                child: Text(
                  option.measurementOptionDescription ?? 'Unnamed Option',
                  style: textTheme.montserratTitleExtraSmall.copyWith(
                    color: AppColors.black,
                  ),
                ),
              );
            }).toList(),
            onChanged: onChanged,
            icon: const Icon(
              Icons.keyboard_arrow_down,
              // color: AppColors.primaryBlue,
            ),
          ),
          // Camera section
          if (showCameraIcon) ...[
            const Gap(16),
            PhotoUploadWidget(
              selectedImages: selectedImages,
              errorText: photoErrorText,
              photoTag: photoTag,
              onCameraPressed: () {
                if (onCameraTap != null) {
                  onCameraTap!();
                }
              },
              onImagesTap: () {
                if (onCameraTap != null) {
                  onCameraTap!();
                }
              },
            ),
          ],
        ],
      ),
    );
  }
}
