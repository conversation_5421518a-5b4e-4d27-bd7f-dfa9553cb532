import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';

/// A reusable widget for displaying measurement image thumbnails
/// Handles network images, local files, loading states, and error cases
class MeasurementThumbnailWidget extends StatelessWidget {
  /// The image URL or local file path to display
  final String? imageUrl;
  
  /// Size of the thumbnail (width and height)
  final double size;
  
  /// Border radius for the thumbnail
  final double borderRadius;
  
  /// Whether to show a placeholder when no image is available
  final bool showPlaceholder;

  const MeasurementThumbnailWidget({
    super.key,
    this.imageUrl,
    this.size = 60.0,
    this.borderRadius = 8.0,
    this.showPlaceholder = false,
  });

  /// Determines if the image path is a network URL
  bool _isNetworkImage(String imagePath) {
    return imagePath.startsWith('http://') || imagePath.startsWith('https://');
  }

  /// Builds the image widget based on the image source type
  Widget _buildImageWidget() {
    if (imageUrl == null || imageUrl!.isEmpty) {
      return _buildPlaceholder();
    }

    if (_isNetworkImage(imageUrl!)) {
      return _buildNetworkImage();
    } else {
      return _buildLocalImage();
    }
  }

  /// Builds a network image with caching and error handling
  Widget _buildNetworkImage() {
    return CachedNetworkImage(
      imageUrl: imageUrl!,
      width: size,
      height: size,
      fit: BoxFit.cover,
      placeholder: (context, url) => _buildLoadingPlaceholder(),
      errorWidget: (context, url, error) => _buildErrorPlaceholder(),
    );
  }

  /// Builds a local file image with error handling
  Widget _buildLocalImage() {
    final file = File(imageUrl!);
    
    return Image.file(
      file,
      width: size,
      height: size,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) => _buildErrorPlaceholder(),
    );
  }

  /// Builds a loading placeholder
  Widget _buildLoadingPlaceholder() {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: AppColors.blackTint3,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: const Center(
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryBlue),
          ),
        ),
      ),
    );
  }

  /// Builds an error placeholder
  Widget _buildErrorPlaceholder() {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: AppColors.blackTint3,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Icon(
        Icons.broken_image_outlined,
        color: AppColors.blackTint1,
        size: size * 0.4,
      ),
    );
  }

  /// Builds a placeholder when no image is available
  Widget _buildPlaceholder() {
    if (!showPlaceholder) {
      return const SizedBox.shrink();
    }
    
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: AppColors.blackTint3,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Icon(
        Icons.image_outlined,
        color: AppColors.blackTint1,
        size: size * 0.4,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Don't render anything if no image and no placeholder requested
    if ((imageUrl == null || imageUrl!.isEmpty) && !showPlaceholder) {
      return const SizedBox.shrink();
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(borderRadius),
      child: _buildImageWidget(),
    );
  }
}
