import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:storetrack_app/core/constants/app_constants.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/entities/previous_tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/store_history/store_history_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/store_history/store_history_state.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/store_history_card.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

@RoutePage()
class StoreHistoryPage extends StatelessWidget {
  final int storeId;
  final int taskId;

  const StoreHistoryPage({
    super.key,
    required this.storeId,
    required this.taskId,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<StoreHistoryCubit>(),
      child: _StoreHistoryPageContent(
        storeId: storeId,
        taskId: taskId,
      ),
    );
  }
}

class _StoreHistoryPageContent extends StatefulWidget {
  final int storeId;
  final int taskId;

  const _StoreHistoryPageContent({
    required this.storeId,
    required this.taskId,
  });

  @override
  State<_StoreHistoryPageContent> createState() =>
      _StoreHistoryPageContentState();
}

class _StoreHistoryPageContentState extends State<_StoreHistoryPageContent>
    with SingleTickerProviderStateMixin {
  String actualDeviceUid = '';
  late String actualUserId;
  final String actualAppVersion = AppConstants.appVersion;
  late String actualUserToken;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );
    _animationController.forward();
    _initializeData();
  }

  Future<void> _initializeData() async {
    try {
      // Get user ID, token, and device ID from DataManager
      final dataManager = sl<DataManager>();
      actualUserId = await dataManager.getUserId() ?? "0";
      actualUserToken = await dataManager.getAuthToken() ?? "0";
      actualDeviceUid = await dataManager.getOrCreateDeviceId();

      // Fetch previous tasks
      if (mounted) {
        context.read<StoreHistoryCubit>().fetchPreviousTasks(
              PreviousTasksRequestEntity(
                userId: int.tryParse(actualUserId) ?? 0,
                token: actualUserToken,
                deviceUid: actualDeviceUid,
                appversion: actualAppVersion,
                taskId: widget.taskId,
                specificTaskId: 0,
              ),
            );
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to initialize store history: ${e.toString()}',
        );
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<StoreHistoryCubit, StoreHistoryState>(
      listener: (context, state) {
        if (state is StoreHistoryError) {
          SnackBarService.error(
            context: context,
            message: state.message,
          );
        }
      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: AppColors.lightGrey2.withValues(alpha: 0.9),
          appBar: const CustomAppBar(
            title: 'Store history',
          ),
          body: FadeTransition(
            opacity: _fadeAnimation,
            child: _buildBody(state),
          ),
        );
      },
    );
  }

  Widget _buildBody(StoreHistoryState state) {
    if (state is StoreHistoryLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppColors.primaryBlue,
        ),
      );
    } else if (state is StoreHistoryLoaded) {
      return state.previousTasks.isEmpty
          ? _buildEmptyState()
          : _buildTaskList(state.previousTasks);
    } else if (state is StoreHistoryError) {
      return _buildErrorState(state.message);
    } else {
      return _buildEmptyState();
    }
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(0),
      child:
          const EmptyState(message: 'No previous task information available'),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.blackTint1,
            ),
            const SizedBox(height: 16),
            Text(
              'Error',
              style: Theme.of(context).textTheme.montserratTitleExtraSmall,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.montserratParagraphSmall,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => _initializeData(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryBlue,
                foregroundColor: Colors.white,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTaskList(List<PreviousTaskEntity> previousTasks) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: previousTasks.length,
      itemBuilder: (context, index) {
        final task = previousTasks[index];
        return StoreHistoryCard(
          task: task,
          onTap: () => _onTaskTap(task),
        );
      },
    );
  }

  void _onTaskTap(PreviousTaskEntity task) {
    // Navigate to store history items page with specific task ID
    if (mounted && task.taskId != null) {
      context.router.push(StoreHistoryItemsRoute(
        storeId: widget.storeId,
        taskId: task.taskId!,
      ));
    }
  }
}
